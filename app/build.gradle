plugins {
    id 'com.android.application'
}

android {
    signingConfigs {
        release {
            keyAlias 'android'
            keyPassword 'android'
            storeFile file('cashier_helper.jks')
            storePassword 'android'
        }
    }

    namespace 'com.yxl.cashier.helper'
    compileSdk 34

    defaultConfig {
        applicationId "com.yxl.cashier.helper"
        minSdk 21
        targetSdk 34
        versionCode 1
        versionName "1.0.1"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
        debug {
            //混淆
            minifyEnabled false
            debuggable true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    aaptOptions {
        additionalParameters '--auto-add-overlay'
        ignoreAssetsPattern "!.svn:!.git:.*:!CVS:!thumbs.db:!picasa.ini:!*.scc:*~"
    }

    // 第三方本地库引入所需
    packagingOptions {
        exclude 'META-INF/gradle/incremental.annotation.processors'
        exclude 'META-INF/library_release.kotlin_module'
    }

    applicationVariants.all { variant ->
        variant.outputs.all {
            def fileName
            def date = new Date()
            def formatterDate = date.format('yyyMMdd')
            fileName = "cashier_helper-${variant.baseName}-${variant.versionName}-${formatterDate}.apk"
            outputFileName = fileName
        }
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation fileTree(include: ['*.aar'], dir: 'libs')
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.9.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'

    implementation project(path: ':libyxlcommon')
    implementation project(path: ':libupdate')
    implementation project(':gt')

    //butterknige库
    api 'com.jakewharton:butterknife:10.2.3'
    annotationProcessor 'com.jakewharton:butterknife-compiler:10.2.3'

    //smartRefreshLayout 下拉刷新
    implementation 'io.github.scwang90:refresh-layout-kernel:2.0.5'
    implementation 'io.github.scwang90:refresh-header-classics:2.0.5'    //经典刷新头
    implementation 'io.github.scwang90:refresh-footer-classics:2.0.5'    //经典加载

    implementation 'com.google.code.gson:gson:2.1'

    //状态栏
    api 'com.qmuiteam:qmui:1.1.12'

    //Picker
    implementation 'com.github.gzu-liyujiang.AndroidPicker:WheelPicker:4.1.11'

    //图像识别
    implementation 'com.rmtheis:tess-two:9.1.0'
    
    implementation 'com.google.mlkit:text-recognition:16.0.0'
    implementation 'com.google.mlkit:text-recognition-chinese:16.0.0'
    implementation 'com.tencent.bugly:crashreport:latest.release'

}