<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        tools:ignore="ScopedStorage" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />

    <!-- 允许程序添加悬浮窗 -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />

    <uses-permission
        android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
        tools:ignore="ScopedStorage" />

    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />

    <uses-feature android:name="android.hardware.camera" />
    <uses-feature android:name="android.hardware.camera.autofocus" />

    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <queries>
        <intent>
            <action android:name="android.intent.action.TTS_SERVICE" />
        </intent>
    </queries>

    <!--广播-开机启动-->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />

    <application
        android:name=".MyApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        tools:replace="android:name"
        tools:targetApi="33">
        <!--android:hardwareAccelerated="false" //硬件加速-->

        <!-- 注册GT悬浮窗 -->
        <service
            android:name=".gt.CashierFloatingWindow"
            android:exported="true"
            android:foregroundServiceType="mediaProjection" />

        <!-- 注册GT悬浮窗-截图（设置识别区域） -->
        <service
            android:name=".gt.CropFloatingWindow"
            android:exported="true"
            android:foregroundServiceType="mediaProjection" />

        <!-- 注册GT悬浮窗-登录 -->
        <service
            android:name=".gt.LoginFloatingWindow"
            android:exported="true" />

        <!-- 注册GT悬浮窗-主函数 -->
        <service
            android:name=".gt.MainFloatingWindow"
            android:exported="true" />

        <!--注册GT悬浮窗-收银结果-->
        <service
            android:name=".gt.CashierResultFloatingWindow"
            android:exported="true" />

        <!-- 开机自启广播 -->
        <receiver
            android:name=".receiver.BootCompleteReceiver"
            android:enabled="true"
            android:exported="true">
            <!-- 接收启动完成的广播 -->
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>

        <!--白页-->
        <activity
            android:name=".LauncherActivity"
            android:exported="true"
            android:windowSoftInputMode="stateHidden|adjustPan">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

        </activity>

    </application>

</manifest>