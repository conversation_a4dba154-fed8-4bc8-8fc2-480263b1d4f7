package com.yxl.cashier.helper.utils;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.view.View;

/**
 * Describe:view方法
 * Created by jingang on 2024/1/26
 */
public class ViewUtils {
    /**
     * view的渐显和渐隐效果
     *
     * @param view
     */
    public static void fadeInAndFadeOut(View view) {
        // 渐显动画
        ObjectAnimator fadeInAnim = ObjectAnimator.ofFloat(view, "alpha", 0f, 1f); // 从完全透明到完全不透明
        fadeInAnim.setDuration(2000); // 设置动画时长为1秒

        // 渐隐动画
        ObjectAnimator fadeOutAnim = ObjectAnimator.ofFloat(view, "alpha", 1f, 0f); // 从完全不透明到完全透明
        fadeOutAnim.setDuration(2000); // 设置动画时长为1秒

        // 使用AnimatorSet将两个动画串联起来，先执行渐显再执行渐隐
        AnimatorSet animSet = new AnimatorSet();
        animSet.playSequentially(fadeInAnim, fadeOutAnim);

        // 在渐显动画结束后延迟几秒开始渐隐动画
        animSet.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                if (animation == fadeInAnim) {
                    // 渐显动画结束，延迟2秒后开始执行渐隐动画
                    fadeOutAnim.setStartDelay(2000);//设置延迟2秒
                    fadeOutAnim.start();
                }
            }

            @Override
            public void onAnimationCancel(Animator animation) {
            }

            @Override
            public void onAnimationRepeat(Animator animation) {
            }
        });

        // 开始执行整个动画序列
        animSet.start();
    }
}
