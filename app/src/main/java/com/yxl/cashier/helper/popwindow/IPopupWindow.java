package com.yxl.cashier.helper.popwindow;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.yxl.cashier.helper.R;

/**
 * Describe:
 * Created by jingang on 2023/12/29
 */
@SuppressLint({"StaticFieldLeak", "NonConstantResourceId"})
public class IPopupWindow extends PopupWindow implements View.OnClickListener {
    private static Context mContext;
    private static String msg, positiveMsg;
    private LinearLayout lin;
    private TextView tvMsg, tvCancel, tvConfirm;

    public IPopupWindow(Context context) {
        super(context);
        View view = View.inflate(context, R.layout.popup_default, null);
        setContentView(view);
        lin = view.findViewById(R.id.linPopup);
        tvMsg = view.findViewById(R.id.tvPopupMsg);
        tvCancel = view.findViewById(R.id.tvPopupCancel);
        tvConfirm = view.findViewById(R.id.tvPopupConfirm);
        lin.setOnClickListener(this);
        tvCancel.setOnClickListener(this);
        tvConfirm.setOnClickListener(this);

        tvMsg.setText(msg);
        tvConfirm.setText(positiveMsg);
    }

    public static void showDialog(Context context, int width, int height, View viewShow, String msg, String positiveMsg, MyListener listener) {
        IPopupWindow.mContext = context;
        IPopupWindow.msg = msg;
        IPopupWindow.positiveMsg = positiveMsg;

        IPopupWindow popupWindow = new IPopupWindow(context);
        popupWindow.setListener(listener);
        popupWindow.setWidth(width + 2);
        popupWindow.setHeight(height + 4);
        //设置可以获取焦点
        popupWindow.setFocusable(false);
        //设置可以触摸弹出框以外的区域
        popupWindow.setOutsideTouchable(true);
        //放在具体控件下方
        popupWindow.showAsDropDown(viewShow);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.linPopup:
                dismiss();
                break;
            case R.id.tvPopupCancel:
                //取消
                dismiss();
                break;
            case R.id.tvPopupConfirm:
                //确认
                if (listener != null) {
                    listener.onClick();
                    dismiss();
                }
                break;
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onClick();
    }

}
