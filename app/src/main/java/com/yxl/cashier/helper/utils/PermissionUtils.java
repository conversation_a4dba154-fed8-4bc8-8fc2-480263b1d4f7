package com.yxl.cashier.helper.utils;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Build;

import androidx.annotation.RequiresApi;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;

/**
 * 检查权限/权限数组
 * request权限
 */
public class PermissionUtils {

    /**
     * 检查多个权限
     * <p>
     * 检查权限
     *
     * @param context Context
     * @return true 已经拥有所有check的权限 false存在一个或多个未获得的权限
     */
    public static boolean checkPermissionsGroup(Context context,String[] permissions) {
        for (String permission : permissions) {
            if (!checkPermission(context, permission)) {
                return false;
            }
        }
        return true;
    }


    /**
     * 检查单个权限
     *
     * @param context    Context
     * @param permission 权限
     * @return boolean
     */
    public static boolean checkPermission(Context context, String permission) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {
            return true;
        }
        return ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED;
    }
    /**
     * 申请权限
     *
     * @param activity    Activity
     * @param requestCode 请求码
     */
    public static void requestPermissions(Activity activity, String[] permissions,int requestCode) {
        // 先检查是否已经授权
        if (!checkPermissionsGroup(activity, permissions)) {
            ActivityCompat.requestPermissions(activity, permissions, requestCode);
        }
    }


    /**
     * 申请权限
     *
     * @param fragment    Fragment
     * @param requestCode 请求码
     */
    public static void requestPermissions(Fragment fragment,String[] permissions, int requestCode) {
        // 先检查是否已经授权
        if (!checkPermissionsGroup(fragment.getActivity(),permissions)) {
            fragment.requestPermissions(permissions, requestCode);
        }
    }


}
