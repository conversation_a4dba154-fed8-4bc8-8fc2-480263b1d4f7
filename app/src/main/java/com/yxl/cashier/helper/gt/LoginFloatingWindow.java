package com.yxl.cashier.helper.gt;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.graphics.BitmapFactory;
import android.os.Build;
import android.text.Editable;
import android.text.InputType;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.allenliu.versionchecklib.core.http.HttpParams;
import com.allenliu.versionchecklib.v2.AllenVersionChecker;
import com.allenliu.versionchecklib.v2.builder.DownloadBuilder;
import com.allenliu.versionchecklib.v2.builder.NotificationBuilder;
import com.allenliu.versionchecklib.v2.builder.UIData;
import com.allenliu.versionchecklib.v2.callback.CustomDownloadFailedListener;
import com.allenliu.versionchecklib.v2.callback.CustomDownloadingDialogListener;
import com.allenliu.versionchecklib.v2.callback.CustomVersionDialogListener;
import com.allenliu.versionchecklib.v2.callback.RequestVersionListener;
import com.google.gson.Gson;
import com.gsls.gt.GT;
import com.yxl.cashier.helper.BuildConfig;
import com.yxl.cashier.helper.LauncherActivity;
import com.yxl.cashier.helper.R;
import com.yxl.cashier.helper.ZURL;
import com.yxl.cashier.helper.bean.SettingData;
import com.yxl.cashier.helper.bean.VersionData;
import com.yxl.cashier.helper.dialog.BaseDialog;
import com.yxl.cashier.helper.popwindow.PrivacyPopup;
import com.yxl.cashier.helper.utils.ViewUtils;
import com.yxl.commonlibrary.Constants;
import com.yxl.commonlibrary.base.BaseApplication;
import com.yxl.commonlibrary.base.BaseData;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DensityUtils;
import com.yxl.commonlibrary.utils.FileUtils;
import com.yxl.commonlibrary.utils.NetWorkUtils;
import com.yxl.commonlibrary.utils.PackageUtils;
import com.yxl.commonlibrary.utils.SpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * Describe:悬浮窗（登录）
 * Created by jingang on 2024/1/19
 */
@SuppressLint("NonConstantResourceId")
@GT.Annotations.GT_AnnotationFloatingWindow(R.layout.floating_login)
public class LoginFloatingWindow extends GT.GT_FloatingWindow.AnnotationFloatingWindow {
    @GT.Annotations.GT_View(R.id.vTop)
    View mV;
    @GT.Annotations.GT_View(R.id.linToast)
    LinearLayout linToast;
    @GT.Annotations.GT_View(R.id.ivToast)
    ImageView ivToast;
    @GT.Annotations.GT_View(R.id.tvToast)
    TextView tvToast;
    @GT.Annotations.GT_View(R.id.etAccount)
    EditText etAccount;
    @GT.Annotations.GT_View(R.id.ivAccountClear)
    ImageView ivAccountClear;
    @GT.Annotations.GT_View(R.id.etPwd)
    EditText etPwd;
    @GT.Annotations.GT_View(R.id.ivPwdClear)
    ImageView ivPwdClear;
    @GT.Annotations.GT_View(R.id.ivEye)
    ImageView ivEye;
    @GT.Annotations.GT_View(R.id.ivAuto)
    ImageView ivAuto;
    @GT.Annotations.GT_View(R.id.ivRemember)
    ImageView ivRemember;
    @GT.Annotations.GT_View(R.id.tvLogin)
    TextView tvLogin;
    @GT.Annotations.GT_View(R.id.tvPrivacy)
    TextView tvPrivacy;
    @GT.Annotations.GT_View(R.id.linLoading)
    LinearLayout linLoading;

    private boolean isEye;//密码可见
    private String account, pwd;
    private int width, height;

    @Override
    protected void initView(View view) {
        super.initView(view);
        setDrag(true);//设置可拖动
        linToast.setAlpha(0f);
        etPwd.setInputType(InputType.TYPE_TEXT_VARIATION_PASSWORD | InputType.TYPE_CLASS_TEXT);//设置密码不可见
        etAccount.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                account = s.toString().trim();
                if (TextUtils.isEmpty(account)) {
                    ivAccountClear.setVisibility(View.GONE);
                } else {
                    ivAccountClear.setVisibility(View.VISIBLE);
                }
                setTextBg();
            }
        });
        etPwd.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                pwd = s.toString().trim();
                if (TextUtils.isEmpty(pwd)) {
                    ivPwdClear.setVisibility(View.GONE);
                } else {
                    ivPwdClear.setVisibility(View.VISIBLE);
                }
                setTextBg();
            }
        });
        ivAuto.setSelected(BaseApplication.getInstance().isAuto());
        ivRemember.setSelected(BaseApplication.getInstance().isRemember());
        if (BaseApplication.getInstance().isAuto() && BaseApplication.isAuto) {
            etAccount.setText(BaseApplication.getInstance().getAccount());
            etPwd.setText(BaseApplication.getInstance().getPwd());
            postLogin();
            return;
        }
        if (BaseApplication.getInstance().isRemember()) {
            etAccount.setText(BaseApplication.getInstance().getAccount());
            etPwd.setText(BaseApplication.getInstance().getPwd());
        }
        setTextBg();
        checkUpgrade();
    }

    @Override
    public void onCreate() {
        super.onCreate();
        width = getWidth() / 10 * 4;
        height = getHeight() / 10 * 6;
        updateView(width, height);
        setGetFocus(true);//是否获取焦点，获取焦点后才能进行弹出软键盘
        //绝对位置（屏幕中间）
        setXY(getWidth() / 2 - width / 2, getHeight() / 2 - height / 2);
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        // 将Service设置为前台服务
        createNotificationChannel();
        return START_STICKY;
    }

    /**
     * 创建前台通知
     */
    private void createNotificationChannel() {
        //Android8.0
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationManager notificationManager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
            NotificationChannel channel = new NotificationChannel(String.valueOf(Constants.notification_id),
                    Constants.notification_name,
                    NotificationManager.IMPORTANCE_MIN);
            channel.setSound(null, null); // 关闭声音
            channel.enableVibration(false); // 禁止震动
            notificationManager.createNotificationChannel(channel);
        }

        Notification.Builder builder = new Notification.Builder(this); //获取一个Notification构造器
        Intent nfIntent = new Intent(this, LauncherActivity.class); //点击后跳转的界面，可以设置跳转数据

        //普通notification适配
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            builder.setChannelId(String.valueOf(Constants.notification_id));
        }

        builder.setContentIntent(PendingIntent.getActivity(this, 0, nfIntent, 0)) // 设置PendingIntent
                .setLargeIcon(BitmapFactory.decodeResource(this.getResources(), R.mipmap.ic_launcher)) // 设置下拉列表中的图标(大图标)
                .setSmallIcon(R.mipmap.ic_launcher) // 设置状态栏内的小图标
                .setContentText("is running......") // 设置上下文内容
                .setVibrate(null)
                .setSound(null)
                .setLights(0, 0, 0)
                .setWhen(System.currentTimeMillis()); // 设置该通知发生的时间

        Notification notification = builder.build(); // 获取构建好的Notification
        startForeground(Constants.notification_id, notification);
    }

    @GT.Annotations.GT_Click({R.id.ivClose, R.id.ivAccountClear, R.id.ivPwdClear, R.id.ivEye, R.id.ivAuto, R.id.ivRemember, R.id.tvLogin, R.id.tvPrivacy,
            R.id.tvVersion, R.id.linLoading})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivClose:
                GT.startFloatingWindow(this, CashierFloatingWindow.class);
                finish();
                break;
            case R.id.ivAccountClear:
                //请输手机号输入
                etAccount.setText("");
                break;
            case R.id.ivPwdClear:
                //清除密码输入
                etPwd.setText("");
                break;
            case R.id.ivEye:
                //密码可见
                isEye = !isEye;
                if (isEye) {
                    etPwd.setInputType(InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD);//设置密码可见
                    ivEye.setImageResource(R.mipmap.ic_eye001);
                } else {
                    etPwd.setInputType(InputType.TYPE_TEXT_VARIATION_PASSWORD | InputType.TYPE_CLASS_TEXT);//设置密码不可见
                    ivEye.setImageResource(R.mipmap.ic_eye002);
                }
                break;
            case R.id.ivAuto:
                //自动登录
                if (BaseApplication.getInstance().isAuto()) {
                    ivAuto.setSelected(false);
                    SpUtils.getInstance().put(Constants.auto, "");
                } else {
                    ivAuto.setSelected(true);
                    SpUtils.getInstance().put(Constants.auto, Constants.auto);
                }
                break;
            case R.id.ivRemember:
                //记住密码
                if (BaseApplication.getInstance().isRemember()) {
                    ivRemember.setSelected(false);
                    SpUtils.getInstance().put(Constants.remember, "");
                } else {
                    ivRemember.setSelected(true);
                    SpUtils.getInstance().put(Constants.remember, Constants.remember);
                }
                break;
            case R.id.tvLogin:
                //登录
                postLogin();
                break;
            case R.id.tvPrivacy:
                //用户协议
                PrivacyPopup.showDialog(this,
                        ZURL.getPrivacy(),
                        width,height,
                        mV,
                        () -> {
                            GT.startFloatingWindow(this, CashierFloatingWindow.class);
                            finish();
                        });
                break;
            case R.id.tvVersion:
                //版本更新（测试）
                checkUpgrade();
                break;
            case R.id.linLoading:
                //加载等待筐
//                linLoading.setVisibility(View.GONE);
                break;
        }
    }

    /**
     * 设置按钮背景
     */
    private void setTextBg() {
        if (TextUtils.isEmpty(account)) {
            tvLogin.setBackgroundResource(R.drawable.shape_green_tm_5);
            return;
        }
        if (account.length() < 11) {
            tvLogin.setBackgroundResource(R.drawable.shape_green_tm_5);
            return;
        }
        if (TextUtils.isEmpty(pwd)) {
            tvLogin.setBackgroundResource(R.drawable.shape_green_tm_5);
            return;
        }
        if (pwd.length() < 6) {
            tvLogin.setBackgroundResource(R.drawable.shape_green_tm_5);
            return;
        }
        tvLogin.setBackgroundResource(R.drawable.shape_green_5);
    }

    /**
     * 显示加载框
     */
    private void showDialog() {
        if (linLoading.getVisibility() != View.VISIBLE) {
            linLoading.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 隐藏加载框
     */
    private void hideDialog() {
        if (linLoading.getVisibility() == View.VISIBLE) {
            linLoading.setVisibility(View.GONE);
        }
    }

    /**
     * 登录
     */
    private void postLogin() {
        if (!NetWorkUtils.isConnect(this)) {
            showToast(1, "无网络连接");
            return;
        }
        account = etAccount.getText().toString().trim();
        pwd = etPwd.getText().toString().trim();
        if (TextUtils.isEmpty(account)) {
            showToast(1, "请输入手机号");
            return;
        }
        if (account.length() < 11) {
            showToast(1, "请输入正确的手机号");
            return;
        }
        if (TextUtils.isEmpty(pwd)) {
            showToast(1, "请输入密码");
            return;
        }
        if (pwd.length() < 6) {
            showToast(1, "密码长度不得小于6位");
            return;
        }
        showDialog();
        showSoftKeyboard(etAccount, false);
        showSoftKeyboard(etPwd, false);
        Map<String, Object> map = new HashMap<>();
        map.put("phone", account);
        map.put("password", pwd);
        Log.e("111111", "url = " + ZURL.getLogin() + " debug = " + BuildConfig.DEBUG);
        RXHttpUtil.requestByBodyPostAsOriginalResponse(this,
                ZURL.getLogin(),
                map,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        Log.e("111111", "登录 = " + s);
                        BaseData data = new Gson().fromJson(s, BaseData.class);
                        if (data == null) {
                            hideDialog();
                            showToast(1, "登录失败");
                            return;
                        }
                        if (data.getCode() != Constants.SUCCESS_CODE) {
                            hideDialog();
                            showToast(1, data.getMsg());
                            return;
                        }
                        if (data.getData() == null) {
                            hideDialog();
                            showToast(1, "登录失败");
                            return;
                        }
                        BaseApplication.getInstance().saveUserInfo(s);
                        SpUtils.getInstance().put(Constants.account, account);
                        SpUtils.getInstance().put(Constants.pwd, pwd);
                        getSetting();
                    }

                    @Override
                    public void onError(String msg, String code) {
                        Log.e("111111", "登录 s = " + msg + " code = " + code);
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 获取配置
     */
    private void getSetting() {
        if (!NetWorkUtils.isConnect(this)) {
            showToast(1, "无网络连接");
            return;
        }
        RXHttpUtil.requestByGetAsResponse(this,
                ZURL.getSetting(),
                new HashMap<>(),
                SettingData.class,
                new RequestListener<SettingData>() {
                    @Override
                    public void success(SettingData settingData) {
                        hideDialog();
                        GT.startFloatingWindow(GT.getActivity(), CashierFloatingWindow.class);
                        finish();
                        if (settingData.getNeedAudit() == 1) {
                            SpUtils.getInstance().put(Constants.process, Constants.process);
                        } else {
                            SpUtils.getInstance().put(Constants.process, "");
                        }
                    }

                    @Override
                    public void onError(String msg, String code) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }


    /**
     * 吐司
     *
     * @param type 0.正确 1.错误
     * @param msg
     */
    private void showToast(int type, String msg) {
        if (type == 0) {
            linToast.setBackgroundResource(R.drawable.shape_green_5);
            ivToast.setImageResource(R.mipmap.ic_toast001);
        } else {
            linToast.setBackgroundResource(R.drawable.shape_red_5);
            ivToast.setImageResource(R.mipmap.ic_toast002);
        }
        tvToast.setText(msg);
        ViewUtils.fadeInAndFadeOut(linToast);
    }

    /**
     * 弹出软键盘
     *
     * @param isShow
     */
    private void showSoftKeyboard(EditText editText, boolean isShow) {
        InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
        if (imm != null) {
            if (isShow) {
                imm.showSoftInput(editText, InputMethodManager.SHOW_IMPLICIT);
            } else {
                imm.hideSoftInputFromWindow(editText.getWindowToken(), 0);
            }
        }
    }

    /************************版本更新start***********************/

    private VersionData versionData;

    /**
     * 更新版本
     */
    private void checkUpgrade() {
        if (!NetWorkUtils.isConnect(this)) {
            showToast(1, "无网络连接");
            return;
        }
        HttpParams map = new HttpParams();
        DownloadBuilder builder = AllenVersionChecker
                .getInstance()
                .requestVersion()
                .setRequestUrl(ZURL.getVersion())
                .setRequestParams(map)
                .request(new RequestVersionListener() {
                    @Nullable
                    @Override
                    public UIData onRequestVersionSuccess(DownloadBuilder downloadBuilder, String result) {
                        Log.e("111111", "version success = " + result);
                        versionData = new Gson().fromJson(result, VersionData.class);
                        if (versionData.getCode() == Constants.SUCCESS_CODE) {
                            if (versionData.getData() == null) return null;
                            //最新版本>当前版本
                            if (versionData.getData().getVersionCode() > PackageUtils.getPackageCode(GT.getActivity())) {
//                                //是否强制更新 0.否 1.是
//                                if (versionData.getData().getForceUpgrade() == 1) {
//                                    downloadBuilder.setForceUpdateListener(() -> {
//                                        finish();
//                                    });
//                                    return crateUIData();
//                                } else {
//                                    String time = String.valueOf(SpUtils.getInstance().get(Constants.LAST_UPDATE, "0"));
//                                    long lastTime = 0;
//                                    if (!TextUtils.isEmpty(time)) {
//                                        lastTime = Long.parseLong(time);
//                                    }
//                                    long currentTime = System.currentTimeMillis();
//                                    if (currentTime - lastTime > 1000 * 60 * 60 * 24) {//一天提醒一次
//                                        SpUtils.getInstance().put(Constants.LAST_UPDATE, String.valueOf(System.currentTimeMillis()));
//                                        return crateUIData();
//                                    }
//                                }

                                String time = String.valueOf(SpUtils.getInstance().get(Constants.LAST_UPDATE, "0"));
                                long lastTime = 0;
                                if (!TextUtils.isEmpty(time)) {
                                    lastTime = Long.parseLong(time);
                                }
                                long currentTime = System.currentTimeMillis();
                                if (currentTime - lastTime > 1000 * 60 * 60 * 24) {//一天提醒一次
                                    GT.startFloatingWindow(LoginFloatingWindow.this, CashierFloatingWindow.class);
                                    finish();
                                    SpUtils.getInstance().put(Constants.LAST_UPDATE, String.valueOf(System.currentTimeMillis()));
                                    //是否强制更新 0.否 1.是
                                    if (versionData.getData().getForceUpgrade() == 1) {
                                        downloadBuilder.setForceUpdateListener(() -> {
                                            finish();
                                        });
                                    }
                                    return crateUIData();
                                }
                            }
                        }
                        return null;
                    }

                    @Override
                    public void onRequestVersionFailure(String message) {
                        Log.e("111111", "version error = " + message);
                    }
                });
        builder.setNotificationBuilder(createCustomNotification());
//        builder.setDownloadAPKPath(this.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS).getPath() + "/");
        builder.setDownloadAPKPath(FileUtils.getPath(this));
        builder.setCustomVersionDialogListener(createCustomDialog());
        builder.setCustomDownloadingDialogListener(createCustomDownloadingDialog());
        builder.setCustomDownloadFailedListener(createCustomDownloadFailedDialog());
        builder.setForceRedownload(true);
        builder.executeMission(this);
    }

    /**
     * 自定义下载中对话框，下载中会连续回调此方法 updateUI
     * 务必用库传回来的context 实例化你的dialog
     *
     * @return
     */
    private CustomDownloadingDialogListener createCustomDownloadingDialog() {
        return new CustomDownloadingDialogListener() {
            @Override
            public Dialog getCustomDownloadingDialog(Context context, int progress, UIData versionBundle) {
                BaseDialog baseDialog = new BaseDialog(context, R.style.dialog_style, R.layout.dialog_download);
                return baseDialog;
            }

            @Override
            public void updateUI(Dialog dialog, int progress, UIData versionBundle) {
                TextView tvProgress = dialog.findViewById(R.id.tvProgress);
                ProgressBar progressBar = dialog.findViewById(R.id.pb);
                progressBar.setProgress(progress);
                tvProgress.setText(getString(R.string.versionchecklib_progress, progress));
            }
        };
    }

    /**
     * 务必用库传回来的context 实例化你的dialog
     *
     * @return
     */
    private CustomDownloadFailedListener createCustomDownloadFailedDialog() {
        return (context, versionBundle) -> {
            BaseDialog baseDialog = new BaseDialog(context, R.style.dialog_style, R.layout.dialog_download_failed);
            return baseDialog;
        };
    }

    private CustomVersionDialogListener createCustomDialog() {
        return (context, versionBundle) -> {
            BaseDialog dialog = new BaseDialog(context, R.style.dialog_style, R.layout.dialog_download_two);
            dialog.setCancelable(false);

            TextView tvTitle, tvContent;
            tvTitle = dialog.findViewById(R.id.tv_title);
            tvContent = dialog.findViewById(R.id.tv_msg);

            tvTitle.setText(versionBundle.getTitle());
            tvContent.setText(versionBundle.getContent());
            dialog.setCanceledOnTouchOutside(false);
            return dialog;
        };
    }

    private NotificationBuilder createCustomNotification() {
        return NotificationBuilder.create()
                .setRingtone(true)
                .setIcon(R.mipmap.ic_launcher)
                .setTicker("custom_ticker")
                .setContentTitle("金圈收银助手升级")
                .setContentText("升级中");
    }

    /**
     * @return
     * @important 使用请求版本功能，可以在这里设置downloadUrl
     * 这里可以构造UI需要显示的数据
     * UIData 内部是一个Bundle
     */
    private UIData crateUIData() {
        UIData uiData = UIData.create();
        uiData.setTitle(versionData.getData().getIntroduce());
        uiData.setDownloadUrl(versionData.getData().getFileUrl());
        uiData.setContent(versionData.getData().getRemark());
        return uiData;
    }

    /************************版本更新end***********************/
}
