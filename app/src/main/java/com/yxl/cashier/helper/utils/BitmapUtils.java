package com.yxl.cashier.helper.utils;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.ColorMatrix;
import android.graphics.ColorMatrixColorFilter;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.Rect;
import android.media.ThumbnailUtils;
import android.util.Log;

/**
 * Describe:bitmap操作
 * Created by jingang on 2024/1/16
 */
public class BitmapUtils {
    private static String tag = "BitmapUtils";

    /**
     * 裁剪
     *
     * @param bitmap
     * @param width
     * @param height
     * @param x
     * @param y
     * @return
     */
    public static Bitmap getCropImg(Bitmap bitmap, int width, int height, int x, int y) {
        Log.e(tag, "w = " + width + " h = " + height + " x =  " + x + " y = " + y + " bitmap w = " + bitmap.getWidth() + " bitmap h = " + bitmap.getHeight());
        // 创建裁剪区域的Rect
        Rect cropRect = new Rect(x, y, x + width, y + height);
        Log.e(tag, "left = " + cropRect.left + " top = " + cropRect.top + " w = " + cropRect.width() + " h = " + cropRect.height());
        // 裁剪图片
        Bitmap croppedBitmap = Bitmap.createBitmap(bitmap, cropRect.left, cropRect.top, cropRect.width(), cropRect.height());

        //释放截屏bitmap
        if (!bitmap.isRecycled()) {
            bitmap.recycle();
            bitmap = null;
        }
        return croppedBitmap;
//        return getHuiDu(croppedBitmap);
    }

    //获取灰度图片
    public static Bitmap getHuiDu(Bitmap bitMap) {
        int width = bitMap.getWidth();//Width 图片宽度
        int height = bitMap.getHeight();// Height 图片高度

        Bitmap bmpGrayscale = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        // bmpGrayscale 设置灰度之后的bitmap对象

        //设置灰度
        Canvas c = new Canvas(bmpGrayscale);
        Paint paint = new Paint();
        ColorMatrix cm = new ColorMatrix();
        cm.setSaturation(0);
        ColorMatrixColorFilter f = new ColorMatrixColorFilter(cm);
        paint.setColorFilter(f);
        c.drawBitmap(bitMap, 0, 0, paint);
        c.save();
        c.restore();
        return convertToBMW(bmpGrayscale);
    }

    /**
     * bitmap二值化
     *
     * @param bmp
     * @return
     */
    public static Bitmap convertToBMW(Bitmap bmp) {
        int width = bmp.getWidth(); // 获取位图的宽
        int height = bmp.getHeight(); // 获取位图的高
        int[] pixels = new int[width * height]; // 通过位图的大小创建像素点数组
        // 设定二值化的域值，默认值为100
        int tmp = 150;
        bmp.getPixels(pixels, 0, width, 0, 0, width, height);
        int alpha = 0xFF << 24;
        for (int i = 0; i < height; i++) {
            for (int j = 0; j < width; j++) {
                int grey = pixels[width * i + j];
                // 分离三原色
                alpha = ((grey & 0xFF000000) >> 24);
                int red = ((grey & 0x00FF0000) >> 16);
                int green = ((grey & 0x0000FF00) >> 8);
                int blue = (grey & 0x000000FF);
                int i1 = 0xFFFF;


                if (red > tmp) {
                    red = 255;
                } else {
                    red = 0;
                }
                if (blue > tmp) {
                    blue = 255;
                } else {
                    blue = 0;
                }
                if (green > tmp) {
                    green = 255;
                } else {
                    green = 0;
                }
                pixels[width * i + j] = alpha << 24 | red << 16 | green << 8
                        | blue;
                if (pixels[width * i + j] == -1) {
                    pixels[width * i + j] = -1;

                } else {
                    pixels[width * i + j] = -16777216;
                }
            }
        }
        // 新建图片
        Bitmap newBmp = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        // 设置图片数据
        newBmp.setPixels(pixels, 0, width, 0, 0, width, height);
        Bitmap resizeBmp = ThumbnailUtils.extractThumbnail(newBmp, width, height);
//        return getHuiDu(resizeBmp, AppApplication.getIntance());
        return resizeBmp;
    }

    /**
     * bitmap亮度和对比度调整
     *
     * @return
     */
    public static Bitmap setLight(Bitmap originalBitmap) {
        // 创建一个新的Bitmap用于存放调整后的结果
        Bitmap adjustedBitmap = Bitmap.createBitmap(originalBitmap.getWidth(), originalBitmap.getHeight(), Bitmap.Config.ARGB_8888);

        // 创建Canvas对象并绑定到新创建的Bitmap上
        Canvas canvas = new Canvas(adjustedBitmap);

        // 创建Paint对象
        Paint paint = new Paint(Paint.ANTI_ALIAS_FLAG);
        ColorMatrix colorMatrix = new ColorMatrix();

        // 调整亮度：向矩阵添加一个偏移量
        float brightness = 0.5f; // 可以设置为负数降低亮度，正数增加亮度
        colorMatrix.set(new float[]{
                1, 0, 0, 0, brightness,
                0, 1, 0, 0, brightness,
                0, 0, 1, 0, brightness,
                0, 0, 0, 1, 0});

//        // 调整对比度：通过改变饱和度系数
//        float contrast = 1.5f; // 可以设置为小于1降低对比度，大于1增加对比度
//        float scale = contrast / 127 + 1;
//        float translate = (-.5f * scale + .5f) * 255;
//        colorMatrix.setSaturation(contrast); // 若要保持原色，则不调用此行
//        colorMatrix.postScale(scale, scale, scale);
//        colorMatrix.postTranslate(translate, translate, translate);

        // 创建ColorMatrixColorFilter并将其应用于Paint
        ColorMatrixColorFilter filter = new ColorMatrixColorFilter(colorMatrix);
        paint.setColorFilter(filter);

        // 在Canvas上绘制原Bitmap，此时会应用颜色矩阵
        canvas.drawBitmap(originalBitmap, 0, 0, paint);

        // 现在adjustedBitmap就是经过亮度和对比度调整后的Bitmap
        return adjustedBitmap;
    }

}
