package com.yxl.cashier.helper.popwindow;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.view.View;
import android.widget.PopupWindow;

import androidx.recyclerview.widget.RecyclerView;

import com.yxl.cashier.helper.R;
import com.yxl.cashier.helper.adapter.DiscountAdapter;
import com.yxl.cashier.helper.bean.OrderInfoData;

import java.util.List;

/**
 * Describe:popup（订单详情-优惠金额）
 * Created by jingang on 2023/5/30
 */
@SuppressLint("NonConstantResourceId")
public class DiscountAmountPopup extends PopupWindow {
    private RecyclerView recyclerView;
    private static List<OrderInfoData.DiscountListBean> list;

    private DiscountAdapter mAdapter;

    @SuppressLint("SetTextI18n")
    public DiscountAmountPopup(Context context) {
        super(context);
        View view = View.inflate(context, R.layout.popup_discount_amount, null);
        setContentView(view);
        recyclerView = view.findViewById(R.id.rvPop);
        setAdapter(context);
    }

    public static void showDialog(Context context,List<OrderInfoData.DiscountListBean> list, View viewShow) {
        DiscountAmountPopup.list = list;
        DiscountAmountPopup popupWindow = new DiscountAmountPopup(context);
        // 设置PopupWindow的背景透明
        popupWindow.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        //设置可以触摸弹出框以外的区域
        popupWindow.setOutsideTouchable(true);
        //放在具体控件下方
        popupWindow.showAsDropDown(viewShow);
    }

    /**
     * 设置适配器
     *
     * @param context
     */
    private void setAdapter(Context context) {
        mAdapter = new DiscountAdapter(context);
        recyclerView.setAdapter(mAdapter);
        if(list!=null){
            mAdapter.setDataList(list);
        }
    }
}
