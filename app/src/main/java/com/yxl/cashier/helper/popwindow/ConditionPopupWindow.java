package com.yxl.cashier.helper.popwindow;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.PopupWindow;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.yxl.cashier.helper.R;
import com.yxl.cashier.helper.adapter.ConditionAdapter;
import com.yxl.cashier.helper.bean.ConditionData;

import java.util.List;

/**
 * Describe:
 * Created by jingang on 2023/12/29
 */
@SuppressLint("StaticFieldLeak")
public class ConditionPopupWindow extends PopupWindow {
    private static Context mContext;
    private static View viewIcon;
    private static List<ConditionData> list;
    private RecyclerView recyclerView;
    private ConditionAdapter mAdapter;

    private final Animation openAnim, closeAnim;

    public ConditionPopupWindow(Context context) {
        super(context);
        View view = View.inflate(context, R.layout.pop_condition, null);
        setContentView(view);
        recyclerView = view.findViewById(R.id.rvPop);
        setAdapter();
        openAnim = AnimationUtils.loadAnimation(context, R.anim.btn_rotate_anim_1);
        closeAnim = AnimationUtils.loadAnimation(context, R.anim.btn_rotate_anim_2);
        openAnim.setFillAfter(true);
        closeAnim.setFillAfter(true);
    }

    public static void showDialog(Context context, View viewIcon, View viewShow, List<ConditionData> list, MyListener listener) {
        ConditionPopupWindow.mContext = context;
        ConditionPopupWindow.viewIcon = viewIcon;
        ConditionPopupWindow.list = list;

        ConditionPopupWindow popupWindow = new ConditionPopupWindow(context);
        popupWindow.setListener(listener);
        popupWindow.setWidth(150);
        popupWindow.setHeight(ViewGroup.LayoutParams.WRAP_CONTENT);
        //new ColorDrawable(0)即为透明背景
        popupWindow.setBackgroundDrawable(new ColorDrawable(0));
        // 设置动画效果
//        popupWindow.setAnimationStyle(R.style.dialog_anim);
        //设置可以获取焦点
        popupWindow.setFocusable(false);
        //设置可以触摸弹出框以外的区域
        popupWindow.setOutsideTouchable(true);
        //放在具体控件下方
        popupWindow.showAsDropDown(viewShow);
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        recyclerView.setLayoutManager(new LinearLayoutManager(mContext));
        mAdapter = new ConditionAdapter(mContext);
        recyclerView.setAdapter(mAdapter);
        mAdapter.setDataList(list);
        mAdapter.setOnItemClickListener((view, position) -> {
            if (listener != null) {
                listener.onClick(list.get(position).getName(), list.get(position).getValue());
                dismiss();
            }
        });
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onClick(String name, int value);
    }

    @Override
    public void showAsDropDown(View anchor) {
        super.showAsDropDown(anchor);
        viewIcon.startAnimation(openAnim);
    }

    @Override
    public void dismiss() {
        super.dismiss();
        viewIcon.startAnimation(closeAnim);
    }
}
