package com.yxl.cashier.helper.bean;

import java.io.Serializable;
import java.util.List;

/**
 * Describe:订单列表（实体类）
 * Created by jingang on 2024/1/17
 */
public class OrderListData implements Serializable {
    /**
     * rows : [{"saleListId":116977,"saleListUnique":17045087912277,"saleListDatetime":"2024-01-08 13:31:09","saleListTotal":"0.02","saleListState":3,"saleListActuallyReceived":"0.01"},{"saleListId":116976,"saleListUnique":17045004033043,"saleListDatetime":"2024-01-06 08:20:21","saleListTotal":"0.03","saleListState":3,"saleListActuallyReceived":"0.01"},{"saleListId":116957,"saleListUnique":1704274271214786,"saleListDatetime":"2024-01-03 17:31:30","saleListTotal":"0.10","saleListState":3,"saleListActuallyReceived":"0.10"},{"saleListId":116956,"saleListUnique":1704273263249386,"saleListDatetime":"2024-01-03 17:14:48","saleListTotal":"0.10","saleListState":3,"saleListActuallyReceived":"0.10"},{"saleListId":116945,"saleListUnique":17042470794141,"saleListDatetime":"2024-01-03 09:58:14","saleListTotal":"42.20","saleListState":3,"saleListActuallyReceived":"43.20"},{"saleListId":116944,"saleListUnique":17042435489656,"saleListDatetime":"2024-01-03 08:59:26","saleListTotal":"42.20","saleListState":3,"saleListActuallyReceived":"43.20"},{"saleListId":116942,"saleListUnique":1704186797081886,"saleListDatetime":"2024-01-02 17:13:33","saleListTotal":"62.00","saleListState":3,"saleListActuallyReceived":"62.00"},{"saleListId":116941,"saleListUnique":1704186750469086,"saleListDatetime":"2024-01-02 17:12:57","saleListTotal":"41.00","saleListState":3,"saleListActuallyReceived":"41.00"},{"saleListId":116940,"saleListUnique":1704186654718286,"saleListDatetime":"2024-01-02 17:11:11","saleListTotal":"17.00","saleListState":3,"saleListActuallyReceived":"17.00"},{"saleListId":116939,"saleListUnique":17041865620396,"saleListDatetime":"2024-01-02 17:09:37","saleListTotal":"42.20","saleListState":3,"saleListActuallyReceived":"43.20"}]
     * total : 3755
     * pageIndex : 1
     * pageSize : 10
     */

    private int total;
    private int pageIndex;
    private int pageSize;
    private List<RowsBean> rows;

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public int getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(int pageIndex) {
        this.pageIndex = pageIndex;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public List<RowsBean> getRows() {
        return rows;
    }

    public void setRows(List<RowsBean> rows) {
        this.rows = rows;
    }

    public static class RowsBean {
        /**
         * 销售订单列表
         * saleListId : 116977
         * saleListUnique : 17045087912277
         * saleListDatetime : 2024-01-08 13:31:09
         * saleListTotal : 0.02
         * saleListState : 3
         * saleListActuallyReceived : 0.01
         */

        private int saleListId;
        private String saleListUnique;
        private String saleListDatetime;
        //        private String saleListTotal;//应收金额
//        private String saleListActuallyReceived;//订单实际收到金额
        private double saleListTotal;//应收金额
        private double saleListActuallyReceived;//订单实际收到金额
        private int saleListState;//付款状态 2-未付款,3-已完成
        private String payStatus;//支付状态：CANCEL-已取消，DOING-未付款，SUCCESS-已支付，FAIL-已关闭

        /**
         * 退款订单列表
         * id : 965
         * retListUnique : 291705999593557
         * retListDatetime : 2024-01-23 16:46:34
         * retListTotal : 0.02
         * retListState : 2
         * retListStateName : 已完成
         * retListHandlestate : 1
         * retListTotalMoney : 0.02
         * staffName : DQ冰激凌(泰盛店)
         */
        private int id;
        private String retListUnique;
        private String retListDatetime;
//        private String retListTotal;//退款总金额
//        private String retListTotalMoney;//实际退款金额
        private double retListTotal;//退款总金额
        private double retListTotalMoney;//实际退款金额
        private String retListState;//退款状态:1-未完成，2-已完成
        private String retListStateName;//退款状态名称
        private String retListHandlestate;//退货申请受理状态：1未处理，2-已受理，3受理完毕;4、已驳回；
        private String staffName;//操作人姓名

        public int getSaleListId() {
            return saleListId;
        }

        public void setSaleListId(int saleListId) {
            this.saleListId = saleListId;
        }

        public String getSaleListUnique() {
            return saleListUnique;
        }

        public void setSaleListUnique(String saleListUnique) {
            this.saleListUnique = saleListUnique;
        }

        public String getSaleListDatetime() {
            return saleListDatetime;
        }

        public void setSaleListDatetime(String saleListDatetime) {
            this.saleListDatetime = saleListDatetime;
        }

//        public String getSaleListTotal() {
//            return saleListTotal;
//        }
//
//        public void setSaleListTotal(String saleListTotal) {
//            this.saleListTotal = saleListTotal;
//        }
//
//        public String getSaleListActuallyReceived() {
//            return saleListActuallyReceived;
//        }
//
//        public void setSaleListActuallyReceived(String saleListActuallyReceived) {
//            this.saleListActuallyReceived = saleListActuallyReceived;
//        }


        public double getSaleListTotal() {
            return saleListTotal;
        }

        public void setSaleListTotal(double saleListTotal) {
            this.saleListTotal = saleListTotal;
        }

        public double getSaleListActuallyReceived() {
            return saleListActuallyReceived;
        }

        public void setSaleListActuallyReceived(double saleListActuallyReceived) {
            this.saleListActuallyReceived = saleListActuallyReceived;
        }

        public int getSaleListState() {
            return saleListState;
        }

        public void setSaleListState(int saleListState) {
            this.saleListState = saleListState;
        }

        public String getPayStatus() {
            return payStatus;
        }

        public void setPayStatus(String payStatus) {
            this.payStatus = payStatus;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getRetListUnique() {
            return retListUnique;
        }

        public void setRetListUnique(String retListUnique) {
            this.retListUnique = retListUnique;
        }

        public String getRetListDatetime() {
            return retListDatetime;
        }

        public void setRetListDatetime(String retListDatetime) {
            this.retListDatetime = retListDatetime;
        }

//        public String getRetListTotal() {
//            return retListTotal;
//        }
//
//        public void setRetListTotal(String retListTotal) {
//            this.retListTotal = retListTotal;
//        }
//
//        public String getRetListTotalMoney() {
//            return retListTotalMoney;
//        }
//
//        public void setRetListTotalMoney(String retListTotalMoney) {
//            this.retListTotalMoney = retListTotalMoney;
//        }


        public double getRetListTotal() {
            return retListTotal;
        }

        public void setRetListTotal(double retListTotal) {
            this.retListTotal = retListTotal;
        }

        public double getRetListTotalMoney() {
            return retListTotalMoney;
        }

        public void setRetListTotalMoney(double retListTotalMoney) {
            this.retListTotalMoney = retListTotalMoney;
        }

        public String getRetListState() {
            return retListState;
        }

        public void setRetListState(String retListState) {
            this.retListState = retListState;
        }

        public String getRetListStateName() {
            return retListStateName;
        }

        public void setRetListStateName(String retListStateName) {
            this.retListStateName = retListStateName;
        }

        public String getRetListHandlestate() {
            return retListHandlestate;
        }

        public void setRetListHandlestate(String retListHandlestate) {
            this.retListHandlestate = retListHandlestate;
        }

        public String getStaffName() {
            return staffName;
        }

        public void setStaffName(String staffName) {
            this.staffName = staffName;
        }
    }
}
