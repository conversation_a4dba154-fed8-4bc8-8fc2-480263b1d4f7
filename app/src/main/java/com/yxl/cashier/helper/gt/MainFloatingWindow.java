package com.yxl.cashier.helper.gt;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.graphics.BitmapFactory;
import android.graphics.drawable.AnimationDrawable;
import android.os.Build;
import android.os.CountDownTimer;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import android.view.KeyEvent;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;


import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.allenliu.versionchecklib.core.http.HttpParams;
import com.allenliu.versionchecklib.v2.AllenVersionChecker;
import com.allenliu.versionchecklib.v2.builder.DownloadBuilder;
import com.allenliu.versionchecklib.v2.builder.NotificationBuilder;
import com.allenliu.versionchecklib.v2.builder.UIData;
import com.allenliu.versionchecklib.v2.callback.CustomDownloadFailedListener;
import com.allenliu.versionchecklib.v2.callback.CustomDownloadingDialogListener;
import com.allenliu.versionchecklib.v2.callback.CustomVersionDialogListener;
import com.allenliu.versionchecklib.v2.callback.RequestVersionListener;
import com.bumptech.glide.Glide;
import com.google.gson.Gson;
import com.gsls.gt.GT;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.cashier.helper.LauncherActivity;
import com.yxl.cashier.helper.MyApplication;
import com.yxl.cashier.helper.R;
import com.yxl.cashier.helper.ZURL;
import com.yxl.cashier.helper.adapter.OrderAdapter;
import com.yxl.cashier.helper.adapter.RefundAdapter;
import com.yxl.cashier.helper.adapter.TotalAdapter;
import com.yxl.cashier.helper.bean.ConditionData;
import com.yxl.cashier.helper.bean.DailySummaryData;
import com.yxl.cashier.helper.bean.OrderInfoData;
import com.yxl.cashier.helper.bean.OrderListData;
import com.yxl.cashier.helper.bean.PayData;
import com.yxl.cashier.helper.bean.PaymentTypeData;
import com.yxl.cashier.helper.bean.RefundData;
import com.yxl.cashier.helper.bean.VersionData;
import com.yxl.cashier.helper.dialog.BaseDialog;
import com.yxl.cashier.helper.popwindow.ConditionPopupWindow;
import com.yxl.cashier.helper.popwindow.DatePopupWindow;
import com.yxl.cashier.helper.popwindow.DiscountAmountPopup;
import com.yxl.cashier.helper.popwindow.IPopupWindow;
import com.yxl.cashier.helper.popwindow.PwdPopup;
import com.yxl.cashier.helper.popwindow.RefundApplyPopup;
import com.yxl.cashier.helper.popwindow.RefundPathPopup;
import com.yxl.cashier.helper.popwindow.RefundPopup;
import com.yxl.cashier.helper.popwindow.RefundResultPopup;
import com.yxl.cashier.helper.utils.SystemTTS;
import com.yxl.cashier.helper.utils.ViewUtils;
import com.yxl.cashier.helper.view.NumberKeyBoardView;
import com.yxl.commonlibrary.AppManager;
import com.yxl.commonlibrary.Constants;
import com.yxl.commonlibrary.base.BaseApplication;
import com.yxl.commonlibrary.base.BaseData;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DFUtils;
import com.yxl.commonlibrary.utils.DateUtils;
import com.yxl.commonlibrary.utils.DensityUtils;
import com.yxl.commonlibrary.utils.FileUtils;
import com.yxl.commonlibrary.utils.NetWorkUtils;
import com.yxl.commonlibrary.utils.PackageUtils;
import com.yxl.commonlibrary.utils.SpUtils;
import com.yxl.commonlibrary.utils.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * Describe:
 * Created by jingang on 2024/1/19
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
@GT.Annotations.GT_AnnotationFloatingWindow(R.layout.floating_main)
public class MainFloatingWindow extends GT.GT_FloatingWindow.AnnotationFloatingWindow {
    private String tag = "MainFloatingWindow";
    @GT.Annotations.GT_View(R.id.v)
    View mV;
    @GT.Annotations.GT_View(R.id.linToast)
    LinearLayout linToast;
    @GT.Annotations.GT_View(R.id.ivToast)
    ImageView ivToast;
    @GT.Annotations.GT_View(R.id.tvToast)
    TextView tvToast;
    @GT.Annotations.GT_View(R.id.etTest)
    EditText etTest;
    /*左侧start*/
    @GT.Annotations.GT_View(R.id.ivLogo)
    ImageView ivLogo;
    @GT.Annotations.GT_View(R.id.lin0)
    LinearLayout lin0;
    @GT.Annotations.GT_View(R.id.iv0)
    ImageView iv0;
    @GT.Annotations.GT_View(R.id.tv0)
    TextView tv0;
    @GT.Annotations.GT_View(R.id.lin1)
    LinearLayout lin1;
    @GT.Annotations.GT_View(R.id.iv1)
    ImageView iv1;
    @GT.Annotations.GT_View(R.id.tv1)
    TextView tv1;
    @GT.Annotations.GT_View(R.id.lin2)
    LinearLayout lin2;
    @GT.Annotations.GT_View(R.id.iv2)
    ImageView iv2;
    @GT.Annotations.GT_View(R.id.tv2)
    TextView tv2;
    @GT.Annotations.GT_View(R.id.lin3)
    LinearLayout lin3;
    @GT.Annotations.GT_View(R.id.iv3)
    ImageView iv3;
    @GT.Annotations.GT_View(R.id.tv3)
    TextView tv3;
    @GT.Annotations.GT_View(R.id.lin4)
    LinearLayout lin4;
    @GT.Annotations.GT_View(R.id.iv4)
    ImageView iv4;
    @GT.Annotations.GT_View(R.id.tv4)
    TextView tv4;
    @GT.Annotations.GT_View(R.id.tvVersionName)
    TextView tvVersionName;
    @GT.Annotations.GT_View(R.id.ivStyle)
    ImageView ivStyle;
    //    private int index = 0;//点击的页卡索引
    private int currentTabIndex = 0;//当前的页卡索引
    private boolean isStyle = true;//true.展开 false.收起
    /*左侧end*/

    /*收银start*/
    @GT.Annotations.GT_View(R.id.linCashier)
    LinearLayout linCashier;
    @GT.Annotations.GT_View(R.id.ivCashierMoney)
    ImageView ivCashierMoney;
    @GT.Annotations.GT_View(R.id.tvCashierMoney)
    TextView tvCashierMoney;
    @GT.Annotations.GT_View(R.id.etCashierCode)
    EditText etCashierCode;
    @GT.Annotations.GT_View(R.id.ivCashierCode)
    ImageView ivCashierCode;
    @GT.Annotations.GT_View(R.id.cashierKeyBoardView)
    NumberKeyBoardView cashierKeyBoardView;
    private boolean isMoney = true;
    private double cashierMoney;
    private String cashierCode;//收款码
    /*收银end*/

    /*退款start*/
    @GT.Annotations.GT_View(R.id.linRefund)
    LinearLayout linRefund;
    @GT.Annotations.GT_View(R.id.etRefundCode)
    EditText etRefundCode;
    @GT.Annotations.GT_View(R.id.ivRefundCode)
    ImageView ivRefundCode;
    @GT.Annotations.GT_View(R.id.refundKeyBoardView)
    NumberKeyBoardView refundKeyBoardView;
    private String refundCode;//商户号
    private RefundPathPopup refundPathPopup;//退款路径弹窗
    /*退款end*/

    /*订单start*/
    @GT.Annotations.GT_View(R.id.linOrder)
    LinearLayout linOrder;
    @GT.Annotations.GT_View(R.id.tvOrderSale)
    TextView tvOrderSale;
    @GT.Annotations.GT_View(R.id.tvOrderRefund)
    TextView tvOrderRefund;
    @GT.Annotations.GT_View(R.id.etOrderSearch)
    EditText etOrderSearch;
    @GT.Annotations.GT_View(R.id.tvOrderType)
    TextView tvOrderType;
    @GT.Annotations.GT_View(R.id.ivOrderType)
    ImageView ivOrderType;
    @GT.Annotations.GT_View(R.id.tvOrderStatus)
    TextView tvOrderStatus;
    @GT.Annotations.GT_View(R.id.ivOrderStatus)
    ImageView ivOrderStatus;
    @GT.Annotations.GT_View(R.id.tvOrderStartTime)
    TextView tvOrderStartTime;
    @GT.Annotations.GT_View(R.id.ivOrderStartTime)
    ImageView ivOrderStartTime;
    @GT.Annotations.GT_View(R.id.tvOrderEndTime)
    TextView tvOrderEndTime;
    @GT.Annotations.GT_View(R.id.ivOrderEndTime)
    ImageView ivOrderEndTime;
    @GT.Annotations.GT_View(R.id.linOrderTop)
    LinearLayout linOrderTop;
    @GT.Annotations.GT_View(R.id.linRefundTop)
    LinearLayout linRefundTop;
    @GT.Annotations.GT_View(R.id.srlOrder)
    SmartRefreshLayout srlOrder;
    @GT.Annotations.GT_View(R.id.rvOrder)
    RecyclerView rvOrder;
    @GT.Annotations.GT_View(R.id.linOrderEmpty)
    LinearLayout linOrderEmpty;

    private int saleListPayment,//支付方式：2-支付宝,3-微信,8-金圈支付,13-云闪付支付
            saleListState,//销售订单订单状态：2-未付款,3-已完成 4.已取消 5.支付失败
            retListState;//退款订单订单状态：1-未退款,2-已退款
    private List<ConditionData> orderTypeList = new ArrayList<>(),//支付方式（改为收款方式）
            orderStatusList = new ArrayList<>(),//销售订单订单状态
            refundStatusList = new ArrayList<>();//退款订单订单状态

    private String saleListUnique,//订单编码（模糊）
            orderPayType,//订单支付方式
            saleListStateName,//销售订单订单状态名称
            retListStateName,//退款订单订单状态名称
            orderStartTime, orderEndTime,
            payStatus;//CANCEL-已取消，DOING-未付款，SUCCESS-已支付，FAIL-支付失败

    private OrderAdapter orderAdapter;
    private List<OrderListData.RowsBean> orderList = new ArrayList<>();
    /*订单end*/

    /*汇总start*/
    @GT.Annotations.GT_View(R.id.linTotal)
    LinearLayout linTotal;
    @GT.Annotations.GT_View(R.id.tvDay0)
    TextView tvDay0;
    @GT.Annotations.GT_View(R.id.tvDay1)
    TextView tvDay1;
    @GT.Annotations.GT_View(R.id.tvDay2)
    TextView tvDay2;
    @GT.Annotations.GT_View(R.id.tvDay3)
    TextView tvDay3;
    @GT.Annotations.GT_View(R.id.tvTotalStartTime)
    TextView tvTotalStartTime;
    @GT.Annotations.GT_View(R.id.ivTotalStartTime)
    ImageView ivTotalStartTime;
    @GT.Annotations.GT_View(R.id.tvTotalEndTime)
    TextView tvTotalEndTime;
    @GT.Annotations.GT_View(R.id.ivTotalEndTime)
    ImageView ivTotalEndTime;
    @GT.Annotations.GT_View(R.id.tvTotalRealTotal)
    TextView tvTotalRealTotal;//实收金额
    @GT.Annotations.GT_View(R.id.tvTotalTotal)
    TextView tvTotalTotal;//交易金额
    @GT.Annotations.GT_View(R.id.tvTotalDiscount)
    TextView tvTotalDiscount;//优惠金额
    @GT.Annotations.GT_View(R.id.tvTotalRefund)
    TextView tvTotalRefund;//退款金额
    @GT.Annotations.GT_View(R.id.srlTotal)
    SmartRefreshLayout srlTotal;
    @GT.Annotations.GT_View(R.id.rvTotal)
    RecyclerView rvTotal;
    @GT.Annotations.GT_View(R.id.linTotalEmpty)
    LinearLayout linTotalEmpty;
    private int day;//0.今日 1.昨日 2.近7日 3.近15日
    private String totalStartTime, totalEndTime;
    private TotalAdapter totalAdapter;
    private List<DailySummaryData.PayTypeListBean> totalList = new ArrayList<>();
    /*汇总end*/

    /*设置start*/
    @GT.Annotations.GT_View(R.id.linSetting)
    LinearLayout linSetting;
    @GT.Annotations.GT_View(R.id.tvSettingBase)
    TextView tvSettingBase;
    @GT.Annotations.GT_View(R.id.tvSettingCashier)
    TextView tvSettingCashier;
    //基础设置
    @GT.Annotations.GT_View(R.id.linSettingBase)
    LinearLayout linSettingBase;
    @GT.Annotations.GT_View(R.id.tvShopName)
    TextView tvShopName;//商户名称
    @GT.Annotations.GT_View(R.id.tvBranchName)
    TextView tvBranchName;//店铺名称
    @GT.Annotations.GT_View(R.id.tvStaffName)
    TextView tvStaffName;//员工名称
    @GT.Annotations.GT_View(R.id.ivStartUp)
    ImageView ivStartUp;
    @GT.Annotations.GT_View(R.id.ivAuto)
    ImageView ivAuto;
    @GT.Annotations.GT_View(R.id.ivPayFinish)
    ImageView ivPayFinish;
    @GT.Annotations.GT_View(R.id.ivRefundFinish)
    ImageView ivRefundFinish;
    //收银设置
    @GT.Annotations.GT_View(R.id.linSettingCashier)
    LinearLayout linSettingCashier;
    @GT.Annotations.GT_View(R.id.ivOcr)
    ImageView ivOcr;
    @GT.Annotations.GT_View(R.id.linProcess)
    LinearLayout linProcess;//退款审核
    @GT.Annotations.GT_View(R.id.ivProcess0)
    ImageView ivProcess0;
    @GT.Annotations.GT_View(R.id.ivProcess1)
    ImageView ivProcess1;
    /*设置end*/

    /*订单详情start*/
    @GT.Annotations.GT_View(R.id.linOrderInfo)
    LinearLayout linOrderInfo;
    @GT.Annotations.GT_View(R.id.ivOrderInfoPayType)
    ImageView ivOrderInfoPayType;//支付方式
    @GT.Annotations.GT_View(R.id.tvOrderInfoPayType)
    TextView tvOrderInfoPayType;
    @GT.Annotations.GT_View(R.id.tvOrderInfoStatus)
    TextView tvOrderInfoStatus;//订单状态

    @GT.Annotations.GT_View(R.id.tvOrderInfoTotal)
    TextView tvOrderInfoTotal;//订单金额
    @GT.Annotations.GT_View(R.id.ivOrderInfoQuestion)
    ImageView ivOrderInfoQuestion;//优惠金额问号
    @GT.Annotations.GT_View(R.id.tvOrderInfoDisCount)
    TextView tvOrderInfoDisCount;//优惠金额
    @GT.Annotations.GT_View(R.id.tvOrderInfoRealTotal)
    TextView tvOrderInfoRealTotal;//实收金额

    @GT.Annotations.GT_View(R.id.tvOrderInfoNo)
    TextView tvOrderInfoNo;//订单编号
    @GT.Annotations.GT_View(R.id.tvOrderInfoSource)
    TextView tvOrderInfoSource;//订单来源
    @GT.Annotations.GT_View(R.id.tvOrderInfoTradeNo)
    TextView tvOrderInfoTradeNo;//支付单号
    @GT.Annotations.GT_View(R.id.tvOrderInfoPayTime)
    TextView tvOrderInfoPayTime;//支付时间
    @GT.Annotations.GT_View(R.id.tvCusName)
    TextView tvCusName;//会员名称
    @GT.Annotations.GT_View(R.id.tvCusPhone)
    TextView tvCusPhone;//会员电话
    @GT.Annotations.GT_View(R.id.ivCusLevel)
    ImageView ivCusLevel;//会员等级
    @GT.Annotations.GT_View(R.id.tvCusLevel)
    TextView tvCusLevel;

    @GT.Annotations.GT_View(R.id.linOrderInfoRefund)
    LinearLayout linOrderInfoRefund;//退款信息
    @GT.Annotations.GT_View(R.id.tvOrderInfoRefundTotal)
    TextView tvOrderInfoRefundTotal;//退款总金额
    @GT.Annotations.GT_View(R.id.srlOrderInfo)
    SmartRefreshLayout srlOrderInfo;
    @GT.Annotations.GT_View(R.id.rvOrderInfo)
    RecyclerView rvOrderInfo;
    @GT.Annotations.GT_View(R.id.tvOrderInfoRefund)
    TextView tvOrderInfoRefund;//退款
    private double total,//订单金额
            realTotal,//实收金额
            discountAmount,//优惠金额
            refundTotal;//退款金额
    private RefundAdapter orderInfoAdapter;
    private List<OrderInfoData.ReturnListBean> orderInfoList = new ArrayList<>();
    private List<OrderInfoData.DiscountListBean> discountList = new ArrayList<>();
    /*订单详情end*/

    /*收银结果start*/
    @GT.Annotations.GT_View(R.id.linCashierResult)
    LinearLayout linCashierResult;
    @GT.Annotations.GT_View(R.id.tvCashierResultTitle)
    TextView tvCashierResultTitle;//收银结果
    @GT.Annotations.GT_View(R.id.ivCashierResultStatus)
    ImageView ivCashierResultStatus;
    @GT.Annotations.GT_View(R.id.tvCashierResultStatus)
    TextView tvCashierResultStatus;
    @GT.Annotations.GT_View(R.id.tvCashierResultMoney)
    TextView tvCashierResultMoney;//收银金额
    @GT.Annotations.GT_View(R.id.tvCashierResultDisCount)
    TextView tvCashierResultDiscount;//优惠金额
    @GT.Annotations.GT_View(R.id.tvCashierResultTips)
    TextView tvCashierResultTips;//收银错误提示
    @GT.Annotations.GT_View(R.id.tvCashierResultDownTimer)
    TextView tvCashierResultDownTimer;//倒计时
    @GT.Annotations.GT_View(R.id.tvCashierResultCancel)
    TextView tvCashierResultCancel;//取消收银
    @GT.Annotations.GT_View(R.id.tvCashierResultClose)
    TextView tvCashierResultClose;//关闭窗口
    @GT.Annotations.GT_View(R.id.tvCashierResultBack)
    TextView tvCashierResultBack;//返回收银台
    /*收银结果end*/

    /*加载等待筐*/
    @GT.Annotations.GT_View(R.id.linLoading)
    LinearLayout linLoading;

    private int page = 1;

    //语音播报
    private SystemTTS systemTTS;

    @RequiresApi(api = Build.VERSION_CODES.M)
    @Override
    protected void initView(View view) {
        super.initView(view);
        setDrag(true);//设置可拖动
        if (MyApplication.systemTTS == null) {
            MyApplication.systemTTS = SystemTTS.getInstance(MyApplication.getInstance());
        }
        systemTTS = MyApplication.systemTTS;
        tvVersionName.setText("v" + PackageUtils.getPackageName(GT.getActivity()));
        linToast.setAlpha(0f);

        //监听扫码枪扫码
        etTest.requestFocus();
        etTest.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == EditorInfo.IME_ACTION_DONE ||
                    (event != null && event.getAction() == KeyEvent.ACTION_DOWN && event.getKeyCode() == KeyEvent.KEYCODE_ENTER)) {
                // 当点击软键盘上的“完成”按钮或按下硬件/虚拟Enter键时执行以下操作
                Log.e(tag, "扫码枪 = " + etTest.getText().toString());
                switch (MyApplication.type) {
                    case 0:
                        //收银
                        cashierCode = etTest.getText().toString();
                        etCashierCode.setText(cashierCode);
                        cashierKeyBoardView.setResultStr(cashierCode);
                        //扫码成功后是否立即支付
                        if (!TextUtils.isEmpty(tvCashierMoney.getText().toString().trim())) {
                            cashierMoney = Double.parseDouble(tvCashierMoney.getText().toString().trim());
                        }
                        if (cashierMoney > 0) {
                            isMoney = true;
                            cashierKeyBoardView.setResultStr(tvCashierMoney.getText().toString().trim());
                            ivCashierMoney.setVisibility(View.VISIBLE);
                            ivCashierCode.setVisibility(View.GONE);
                            postPay();
                        } else {
                            isMoney = false;
                            ivCashierMoney.setVisibility(View.GONE);
                            ivCashierCode.setVisibility(View.VISIBLE);
                        }
                        break;
                    case 1:
                        //退款
                        if (refundPathPopup != null) {
                            refundPathPopup.dismiss();
                        }
                        refundCode = etTest.getText().toString();
                        etRefundCode.setText(refundCode);
                        refundKeyBoardView.setResultStr(refundCode);
                        //扫码成功后是否立即退款
                        if (!TextUtils.isEmpty(refundCode)) {
                            getOrderInfoConfirm();
//                            MyApplication.saleUniqueType = 1;
//                            MyApplication.saleListUnique = refundCode;
//                            MyApplication.type = 5;
//                            fragmentControl();
                        }
                        break;
                    case 2:
                        //订单
                        etOrderSearch.setText(etTest.getText().toString());
                        saleListUnique = etOrderSearch.getText().toString().trim();
                        page = 1;
                        getOrderList();
                        break;
                }
                etTest.setText("");
                return true; // 表示已处理该事件
            }
            return false;
        });
        //获取焦点不弹出软键盘
        etTest.setOnFocusChangeListener((v, hasFocus) -> {
            if (hasFocus) {
                InputMethodManager imm = (InputMethodManager) view.getContext().getSystemService(INPUT_METHOD_SERVICE);
                if (imm != null) {
                    imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
                }
            }
        });

        /***********************收银start**********************/
        ((AnimationDrawable) ivCashierMoney.getDrawable()).start();
        ((AnimationDrawable) ivCashierCode.getDrawable()).start();
        //收银
        cashierKeyBoardView.setOnMValueChangedListener(new NumberKeyBoardView.OnMValueChangedListener() {
            @Override
            public void onChange(String var1) {
                if (isMoney) {
                    if (TextUtils.isEmpty(var1)) {
                        tvCashierMoney.setText("");
                        cashierMoney = 0;
                        MyApplication.cashierMoney = "";
                        return;
                    }
                    cashierMoney = Double.parseDouble(var1);
                    if (cashierMoney > Constants.cashier_max) {
                        cashierMoney = Constants.cashier_max;
                        cashierKeyBoardView.setResultStr(String.valueOf(Constants.cashier_max));
                        MyApplication.cashierMoney = String.valueOf(Constants.cashier_max);
                        tvCashierMoney.setText(String.valueOf(Constants.cashier_max));
                        return;
                    }
                    MyApplication.cashierMoney = var1;
                    tvCashierMoney.setText(var1);

                } else {
                    cashierCode = var1;
                    etCashierCode.setText(var1);
                }
            }

            @Override
            public void onConfirm() {
                //确认
                if (cashierMoney == 0) {
                    showToast(1, "请输入收银金额");
                    return;
                }
                if (TextUtils.isEmpty(cashierCode)) {
                    showToast(1, "请扫描/输入付款码");
                    return;
                }
                postPay();
            }
        });
        //收银：设置监听器捕获Enter键（扫码枪通常会发送这个 11以上不监听）
//        etCashierCode.setOnKeyListener((v, keyCode, event) -> {
//            if (event.getAction() == KeyEvent.ACTION_DOWN) {
//                char pressedKey = (char) event.getUnicodeChar();
//                cashierBarcode += pressedKey;
//            }
//            if (keyCode == KeyEvent.KEYCODE_ENTER && event.getAction() == KeyEvent.ACTION_DOWN) {
//                // 当收到Enter按键（通常是扫码枪结束输入的标志）
//                cashierCode = cashierBarcode.trim();
//                cashierBarcode = "";
//                etCashierCode.setText(cashierCode);
//                cashierKeyBoardView.setResultStr(cashierCode);
//                //扫码成功后是否立即支付
//                if (!TextUtils.isEmpty(tvCashierMoney.getText().toString().trim())) {
//                    cashierMoney = Double.parseDouble(tvCashierMoney.getText().toString().trim());
//                }
//                if (cashierMoney > 0) {
//                    isMoney = true;
//                    ivCashierMoney.setVisibility(View.VISIBLE);
//                    ivCashierCode.setVisibility(View.GONE);
//                    postPay();
//                } else {
//                    isMoney = false;
//                    ivCashierMoney.setVisibility(View.GONE);
//                    ivCashierCode.setVisibility(View.VISIBLE);
//                }
//                return true; // 表示已处理该事件
//            }
//            return false;
//        });
        /***********************收银end**********************/

        /***********************退款start**********************/
        ((AnimationDrawable) ivRefundCode.getDrawable()).start();
        //退款
        refundKeyBoardView.setOnMValueChangedListener(new NumberKeyBoardView.OnMValueChangedListener() {
            @Override
            public void onChange(String var1) {
                refundCode = var1;
                etRefundCode.setText(var1);
            }

            @Override
            public void onConfirm() {
                //确认
                if (TextUtils.isEmpty(refundCode)) {
                    showToast(1, "请扫描/输入商户单号");
                    return;
                }
                getOrderInfoConfirm();
//                MyApplication.saleUniqueType = 1;
//                MyApplication.saleListUnique = etRefundCode.getText().toString().trim();
//                MyApplication.type = 5;
//                fragmentControl();
            }
        });

        //退款：设置监听器捕获Enter键（扫码枪通常会发送这个,11以上不监听）
//        etRefundCode.setOnKeyListener((v, keyCode, event) -> {
//            if (event.getAction() == KeyEvent.ACTION_DOWN) {
//                char pressedKey = (char) event.getUnicodeChar();
//                refundBarcode += pressedKey;
//            }
//            if (keyCode == KeyEvent.KEYCODE_ENTER && event.getAction() == KeyEvent.ACTION_DOWN) {
//                // 当收到Enter按键（通常是扫码枪结束输入的标志）
//                refundCode = refundBarcode;
//                refundBarcode = "";
//                etRefundCode.setText(refundCode);
//                refundKeyBoardView.setResultStr(refundCode);
//                //扫码成功后是否立即退款
//                if (!TextUtils.isEmpty(refundCode)) {
//                    MyApplication.saleUniqueType = 1;
//                    MyApplication.saleListUnique = refundCode;
//                    MyApplication.type = 5;
//                    fragmentControl();
//                }
//                return true; // 表示已处理该事件
//            }
//            return false;
//        });
        /***********************退款end**********************/

        etOrderSearch.setOnEditorActionListener((v, actionId, event) -> {
            saleListUnique = v.getText().toString().trim();
            page = 1;
            getOrderList();
            return true;
        });
        initDataPayType();
        getPaymentTypeList();

        orderStartTime = DateUtils.getOldDate(0);
        orderEndTime = DateUtils.getOldDate(0);
        tvOrderStartTime.setText(orderStartTime);
        tvOrderEndTime.setText(orderEndTime);
        totalStartTime = DateUtils.getOldDate(0);
        totalEndTime = DateUtils.getOldDate(0);
        tvTotalStartTime.setText(totalStartTime);
        tvTotalEndTime.setText(totalEndTime);
        refundPathPopup = new RefundPathPopup(this);

        setAdapter();
        fragmentControl();
    }

    @Override
    public void onCreate() {
        super.onCreate();
        setGetFocus(true);//是否获取焦点，获取焦点后才能进行弹出软键盘
        updateView(DensityUtils.getFloatingWindowWidth(getWidth()), DensityUtils.getFloatingWindowHeight(getHeight()));
        //绝对位置（屏幕中间）
        setXY(getWidth() / 2 - DensityUtils.getFloatingWindowWidth(getWidth()) / 2,
                getHeight() / 2 - DensityUtils.getFloatingWindowHeight(getHeight()) / 2);

    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        // 将Service设置为前台服务
        createNotificationChannel();
        return START_STICKY;
    }

    /**
     * 创建前台通知
     */
    private void createNotificationChannel() {
        //Android8.0
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationManager notificationManager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
            NotificationChannel channel = new NotificationChannel(String.valueOf(Constants.notification_id),
                    Constants.notification_name,
                    NotificationManager.IMPORTANCE_MIN);
            channel.setSound(null, null); // 关闭声音
            channel.enableVibration(false); // 禁止震动
            notificationManager.createNotificationChannel(channel);
        }

        Notification.Builder builder = new Notification.Builder(this); //获取一个Notification构造器
        Intent nfIntent = new Intent(this, LauncherActivity.class); //点击后跳转的界面，可以设置跳转数据

        //普通notification适配
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            builder.setChannelId(String.valueOf(Constants.notification_id));
        }

        builder.setContentIntent(PendingIntent.getActivity(this, 0, nfIntent, 0)) // 设置PendingIntent
                .setLargeIcon(BitmapFactory.decodeResource(this.getResources(), R.mipmap.ic_launcher)) // 设置下拉列表中的图标(大图标)
                .setSmallIcon(R.mipmap.ic_launcher) // 设置状态栏内的小图标
                .setContentText("is running......") // 设置上下文内容
                .setVibrate(null)
                .setSound(null)
                .setLights(0, 0, 0)
                .setWhen(System.currentTimeMillis()); // 设置该通知发生的时间

        Notification notification = builder.build(); // 获取构建好的Notification
        startForeground(Constants.notification_id, notification);
    }

    @GT.Annotations.GT_Click({R.id.lin0, R.id.lin1, R.id.lin2, R.id.lin3, R.id.lin4, R.id.ivStyle,
            R.id.ivCashierClose, R.id.tvCashierMoney, R.id.etCashierCode,
            R.id.ivRefundClose, R.id.ivRefundQuestion,
            R.id.tvOrderSale, R.id.tvOrderRefund, R.id.ivOrderClose, R.id.linOrderType, R.id.linOrderStatus, R.id.linOrderStartTime, R.id.linOrderEndTime, R.id.tvOrderResetting,
            R.id.ivTotalClose, R.id.tvDay0, R.id.tvDay1, R.id.tvDay2, R.id.tvDay3, R.id.linTotalStartTime, R.id.linTotalEndTime,
            R.id.tvSettingBase, R.id.tvSettingCashier, R.id.ivSettingClose, R.id.ivStartUp, R.id.ivAuto, R.id.ivPayFinish, R.id.ivRefundFinish, R.id.tvOcr, R.id.ivProcess0, R.id.ivProcess1, R.id.tvExit, R.id.tvVersion, R.id.tvLoginOut,
            R.id.ivOrderInfoBack, R.id.ivOrderInfoClose, R.id.ivOrderInfoQuestion, R.id.tvOrderInfoRefund,
            R.id.ivCashierResultBack, R.id.ivCashierResultClose, R.id.tvCashierResultCancel, R.id.tvCashierResultClose, R.id.tvCashierResultBack,
            R.id.linLoading})
    public void onClick(View view) {
        switch (view.getId()) {
            /*左侧start*/
            case R.id.lin0:
                //收银
                MyApplication.type = 0;
                fragmentControl();
                break;
            case R.id.lin1:
                //退款
                MyApplication.type = 1;
                fragmentControl();
                break;
            case R.id.lin2:
                //订单
                MyApplication.type = 2;
                fragmentControl();
                break;
            case R.id.lin3:
                //汇总
                MyApplication.type = 3;
                fragmentControl();
                break;
            case R.id.lin4:
                //设置
                MyApplication.type = 4;
                fragmentControl();
                break;
            case R.id.ivStyle:
                //左侧menu样式
                isStyle = !isStyle;
                if (isStyle) {
                    ivLogo.setImageResource(R.mipmap.ic_main_img001);
                    ivStyle.setImageResource(R.mipmap.ic_style001);
                    tv0.setVisibility(View.VISIBLE);
                    tv1.setVisibility(View.VISIBLE);
                    tv2.setVisibility(View.VISIBLE);
                    tv3.setVisibility(View.VISIBLE);
                    tv4.setVisibility(View.VISIBLE);
                } else {
                    ivLogo.setImageResource(R.mipmap.ic_login001);
                    ivStyle.setImageResource(R.mipmap.ic_style002);
                    tv0.setVisibility(View.GONE);
                    tv1.setVisibility(View.GONE);
                    tv2.setVisibility(View.GONE);
                    tv3.setVisibility(View.GONE);
                    tv4.setVisibility(View.GONE);
                }
                break;
            /*左侧end*/

            case R.id.ivCashierClose:
                //收银（收起）
            case R.id.ivRefundClose:
                //退款（收起）
            case R.id.ivOrderClose:
                //订单（收起）
            case R.id.ivTotalClose:
                //汇总（收起）
            case R.id.ivSettingClose:
                //设置（收起）
            case R.id.ivOrderInfoClose:
                //订单详情（收起）
                GT.startFloatingWindow(this, CashierFloatingWindow.class);
                finish();
                break;

            /*收银start*/
            case R.id.tvCashierMoney:
                if (!isMoney) {
                    isMoney = true;
                    ivCashierMoney.setVisibility(View.VISIBLE);
                    ivCashierCode.setVisibility(View.GONE);
                    cashierKeyBoardView.setResultStr(tvCashierMoney.getText().toString().trim());
                }
                break;
            case R.id.etCashierCode:
                if (isMoney) {
                    isMoney = false;
                    ivCashierMoney.setVisibility(View.GONE);
                    ivCashierCode.setVisibility(View.VISIBLE);
                    cashierKeyBoardView.setResultStr(etCashierCode.getText().toString().trim());
                }
                break;
            /*收银end*/

            case R.id.ivRefundQuestion:
                //退款说明
                if (refundPathPopup != null) {
                    if (refundPathPopup.isShowing()) {
                        refundPathPopup.dismiss();
                    } else {
                        refundPathPopup.showAsDropDown(view, -(DensityUtils.getFloatingWindowWidth(getWidth()) - 560 - 120) / 2, 0);
                    }
                }
                break;

            /*订单start*/
            case R.id.tvOrderSale:
                //销售订单
                if (MyApplication.orderType != 0) {
                    MyApplication.orderType = 0;
                    tvOrderSale.setBackgroundResource(R.drawable.shape_f7_top_5);
                    tvOrderSale.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));
                    tvOrderRefund.setBackgroundResource(0);
                    tvOrderRefund.setTextColor(getResources().getColor(R.color.color_333));
                    linOrderTop.setVisibility(View.VISIBLE);
                    linRefundTop.setVisibility(View.GONE);
                    if (TextUtils.isEmpty(orderPayType)) {
                        tvOrderType.setText("收款方式");
                    } else {
                        tvOrderType.setText(orderPayType);
                    }
                    if (TextUtils.isEmpty(saleListStateName)) {
                        tvOrderStatus.setText("订单状态");
                    } else {
                        tvOrderStatus.setText(saleListStateName);
                    }
                    page = 1;
                    getOrderList();
                }
                break;
            case R.id.tvOrderRefund:
                //退款订单
                if (MyApplication.orderType == 0) {
                    MyApplication.orderType = 1;
                    tvOrderRefund.setBackgroundResource(R.drawable.shape_f7_top_5);
                    tvOrderRefund.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));
                    tvOrderSale.setBackgroundResource(0);
                    tvOrderSale.setTextColor(getResources().getColor(R.color.color_333));
                    linOrderTop.setVisibility(View.GONE);
                    linRefundTop.setVisibility(View.VISIBLE);
                    if (TextUtils.isEmpty(orderPayType)) {
                        tvOrderType.setText("收款方式");
                    } else {
                        tvOrderType.setText(orderPayType);
                    }
                    if (TextUtils.isEmpty(retListStateName)) {
                        tvOrderStatus.setText("订单状态");
                    } else {
                        tvOrderStatus.setText(retListStateName);
                    }
                    page = 1;
                    getOrderList();
                }
                break;
            case R.id.linOrderType:
                //收款方式
                ConditionPopupWindow.showDialog(GT.getActivity(), ivOrderType, view, orderTypeList, (name, value) -> {
                    if (!TextUtils.isEmpty(name)) {
                        if (TextUtils.equals(name, "全部")) {
                            orderPayType = "收款方式";
                        } else {
                            orderPayType = name;
                        }
                    }
                    tvOrderType.setText(orderPayType);
                    saleListPayment = value;
                    page = 1;
                    getOrderList();
                });
                break;
            case R.id.linOrderStatus:
                //订单状态
                if (MyApplication.orderType == 0) {
                    //销售订单
                    ConditionPopupWindow.showDialog(GT.getActivity(), ivOrderStatus, view, orderStatusList, (name, value) -> {
                        if (!TextUtils.isEmpty(name)) {
                            if (TextUtils.equals(name, "全部")) {
                                saleListStateName = "订单状态";
                            } else {
                                saleListStateName = name;
                            }
                        }
                        tvOrderStatus.setText(saleListStateName);
                        saleListState = value;
                        page = 1;
                        getOrderList();
                    });
                } else {
                    //退款订单
                    ConditionPopupWindow.showDialog(GT.getActivity(), ivOrderStatus, view, refundStatusList, (name, value) -> {
                        if (!TextUtils.isEmpty(name)) {
                            if (TextUtils.equals(name, "全部")) {
                                retListStateName = "订单状态";
                            } else {
                                retListStateName = name;
                            }
                        }
                        tvOrderStatus.setText(retListStateName);
                        retListState = value;
                        page = 1;
                        getOrderList();
                    });
                }
                break;
            case R.id.linOrderStartTime:
                //开始时间
                DatePopupWindow.showDialog(GT.getActivity(), ivOrderStartTime, view, orderStartTime, orderEndTime, orderStartTime, 0, (startDate, endDate) -> {
                    orderStartTime = startDate;
                    orderEndTime = endDate;
                    tvOrderStartTime.setText(orderStartTime);
                    tvOrderEndTime.setText(orderEndTime);
                    page = 1;
                    getOrderList();
                });
                break;
            case R.id.linOrderEndTime:
                //结束时间
                DatePopupWindow.showDialog(GT.getActivity(), ivOrderEndTime, view, orderStartTime, orderEndTime, orderEndTime, 1, (startDate, endDate) -> {
                    orderStartTime = startDate;
                    orderEndTime = endDate;
                    tvOrderStartTime.setText(orderStartTime);
                    tvOrderEndTime.setText(orderEndTime);
                    page = 1;
                    getOrderList();
                });
                break;
            case R.id.tvOrderResetting:
                //重置条件
                saleListUnique = "";
                etOrderSearch.setText("");
                orderPayType = "收款方式";
                saleListPayment = 0;
                tvOrderType.setText("收款方式");
                if (MyApplication.orderType == 0) {
                    saleListStateName = "订单状态";
                    saleListState = 0;
                } else {
                    retListStateName = "订单状态";
                    retListState = 0;
                }
                tvOrderStatus.setText("订单状态");
                orderStartTime = DateUtils.getOldDate(0);
                orderEndTime = DateUtils.getOldDate(0);
                tvOrderStartTime.setText(orderStartTime);
                tvOrderEndTime.setText(orderEndTime);
                page = 1;
                getOrderList();
                break;
            /*订单end*/

            /*汇总start*/
            case R.id.tvDay0:
                //今日
                if (day != 0) {
                    day = 0;
                    clearTotalTextBg();
                    tvDay0.setBackgroundResource(R.drawable.shape_green_kuang_5);
                    tvDay0.setTextColor(getResources().getColor(R.color.green));
                    totalStartTime = DateUtils.getOldDate(0);
                    totalEndTime = DateUtils.getOldDate(0);
                    tvTotalStartTime.setText(totalStartTime);
                    tvTotalEndTime.setText(totalEndTime);
                    getDailySummary();
                }
                break;
            case R.id.tvDay1:
                //昨日
                if (day != 1) {
                    day = 1;
                    clearTotalTextBg();
                    tvDay1.setBackgroundResource(R.drawable.shape_green_kuang_5);
                    tvDay1.setTextColor(getResources().getColor(R.color.green));
                    totalStartTime = DateUtils.getOldDate(-1);
                    totalEndTime = DateUtils.getOldDate(-1);
                    tvTotalStartTime.setText(totalStartTime);
                    tvTotalEndTime.setText(totalEndTime);
                    getDailySummary();
                }
                break;
            case R.id.tvDay2:
                //近7日
                if (day != 2) {
                    day = 2;
                    clearTotalTextBg();
                    tvDay2.setBackgroundResource(R.drawable.shape_green_kuang_5);
                    tvDay2.setTextColor(getResources().getColor(R.color.green));
                    totalStartTime = DateUtils.getOldDate(-7);
                    totalEndTime = DateUtils.getOldDate(0);
                    tvTotalStartTime.setText(totalStartTime);
                    tvTotalEndTime.setText(totalEndTime);
                    getDailySummary();
                }
                break;
            case R.id.tvDay3:
                //近15日
                if (day != 3) {
                    day = 3;
                    clearTotalTextBg();
                    tvDay3.setBackgroundResource(R.drawable.shape_green_kuang_5);
                    tvDay3.setTextColor(getResources().getColor(R.color.green));
                    totalStartTime = DateUtils.getOldDate(-15);
                    totalEndTime = DateUtils.getOldDate(0);
                    tvTotalStartTime.setText(totalStartTime);
                    tvTotalEndTime.setText(totalEndTime);
                    getDailySummary();
                }
                break;
            case R.id.linTotalStartTime:
                //开始时间
                DatePopupWindow.showDialog(this, ivTotalStartTime, view, totalStartTime, totalEndTime, totalStartTime, 0, (startDate, endDate) -> {
                    day = -1;
                    clearTotalTextBg();
                    totalStartTime = startDate;
                    totalEndTime = endDate;
                    tvTotalStartTime.setText(totalStartTime);
                    tvTotalEndTime.setText(totalEndTime);
                    getDailySummary();
                });
                break;
            case R.id.linTotalEndTime:
                //结束时间
                DatePopupWindow.showDialog(this, ivTotalEndTime, view, totalStartTime, totalEndTime, totalEndTime, 1, (startDate, endDate) -> {
                    day = -1;
                    clearTotalTextBg();
                    totalStartTime = startDate;
                    totalEndTime = endDate;
                    tvTotalStartTime.setText(totalStartTime);
                    tvTotalEndTime.setText(totalEndTime);
                    getDailySummary();
                });
                break;
            /*汇总end*/

            /*设置start*/
            case R.id.tvSettingBase:
                //基础设置
                if (MyApplication.isCashierBase) {
                    MyApplication.isCashierBase = false;
                    tvSettingBase.setBackgroundResource(R.drawable.shape_f7_top_5);
                    tvSettingBase.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));
                    tvSettingCashier.setBackgroundResource(0);
                    tvSettingCashier.setTextColor(getResources().getColor(R.color.color_333));
                    linSettingBase.setVisibility(View.VISIBLE);
                    linSettingCashier.setVisibility(View.GONE);
                }
                break;
            case R.id.tvSettingCashier:
                //收银设置
                if (!MyApplication.isCashierBase) {
                    MyApplication.isCashierBase = true;
                    tvSettingCashier.setBackgroundResource(R.drawable.shape_f7_top_5);
                    tvSettingCashier.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));
                    tvSettingBase.setBackgroundResource(0);
                    tvSettingBase.setTextColor(getResources().getColor(R.color.color_333));
                    linSettingCashier.setVisibility(View.VISIBLE);
                    linSettingBase.setVisibility(View.GONE);
                }
                break;
            case R.id.ivStartUp:
                //开机启动
                if (BaseApplication.getInstance().isStartUp()) {
                    ivStartUp.setSelected(false);
                    SpUtils.getInstance().put(Constants.startUp, "");
                } else {
                    ivStartUp.setSelected(true);
                    SpUtils.getInstance().put(Constants.startUp, Constants.startUp);
                }
                break;
            case R.id.ivAuto:
                //自动登录
                if (BaseApplication.getInstance().isAuto()) {
                    ivAuto.setSelected(false);
                    SpUtils.getInstance().put(Constants.auto, "");
                } else {
                    ivAuto.setSelected(true);
                    SpUtils.getInstance().put(Constants.auto, Constants.auto);
                }
                break;
            case R.id.ivPayFinish:
                //结算完成最小化
                if (BaseApplication.getInstance().isPayFinish()) {
                    ivPayFinish.setSelected(false);
                    SpUtils.getInstance().put(Constants.payFinish, "");
                } else {
                    ivPayFinish.setSelected(true);
                    SpUtils.getInstance().put(Constants.payFinish, Constants.payFinish);
                }
                break;
            case R.id.ivRefundFinish:
                //退款完成最小化
                if (BaseApplication.getInstance().isRefundFinish()) {
                    ivRefundFinish.setSelected(false);
                    SpUtils.getInstance().put(Constants.refundFinish, "");
                } else {
                    ivRefundFinish.setSelected(true);
                    SpUtils.getInstance().put(Constants.refundFinish, Constants.refundFinish);
                }
                break;
            case R.id.tvOcr:
                //设置识别区域
                GT.startFloatingWindow(this, CropFloatingWindow.class);
                finish();
                break;
            case R.id.ivProcess0:
                //退款审核（审核）
                if (!BaseApplication.getInstance().isProcess()) {
                    ivProcess0.setSelected(true);
                    ivProcess1.setSelected(false);
                    PwdPopup.showDialog(this,
                            DensityUtils.getFloatingWindowWidth(getWidth()),
                            DensityUtils.getFloatingWindowHeight(getHeight()),
                            mV,
                            new PwdPopup.MyListener() {
                                @Override
                                public void onPwd(String pwd) {
                                    //确认
                                    settingPwd = pwd;
                                    needAudit = 1;
                                    postSetting();
                                }

                                @Override
                                public void onCancel() {
                                    //取消
                                    ivProcess0.setSelected(false);
                                    ivProcess1.setSelected(true);
                                }
                            });
                }
                break;
            case R.id.ivProcess1:
                //退款审核（不审核）
                if (BaseApplication.getInstance().isProcess()) {
                    ivProcess0.setSelected(false);
                    ivProcess1.setSelected(true);
                    settingPwd = "";
                    needAudit = 2;
                    postSetting();
//                    PwdPopup.showDialog(this,
//                            DensityUtils.getFloatingWindowWidth(getWidth()),
//                            DensityUtils.getFloatingWindowHeight(getHeight()),
//                            mV,
//                            new PwdPopup.MyListener() {
//                                @Override
//                                public void onPwd(String pwd) {
//                                    //确认
//                                    postSetting("", 2);
//                                }
//
//                                @Override
//                                public void onCancel() {
//                                    //取消
//                                    ivProcess0.setSelected(true);
//                                    ivProcess1.setSelected(false);
//                                }
//                            });
                }
                break;
            case R.id.tvExit:
                //退出程序
                IPopupWindow.showDialog(this,
                        DensityUtils.getFloatingWindowWidth(getWidth()),
                        DensityUtils.getFloatingWindowHeight(getHeight()),
                        mV, "确认退出程序？", "确认", () -> {
                            finish();
                            AppManager.getInstance().finishAllActivity();
                        });
                break;
            case R.id.tvVersion:
                //检查更新
                checkUpgrade();
                break;
            case R.id.tvLoginOut:
                //退出登录
                IPopupWindow.showDialog(this,
                        DensityUtils.getFloatingWindowWidth(getWidth()),
                        DensityUtils.getFloatingWindowHeight(getHeight()),
                        mV, "确认退出登录？", "确认", () -> {
                            BaseApplication.getInstance().saveUserInfo("");
                            BaseApplication.isAuto = false;//是否自动登录
                            GT.startFloatingWindow(GT.getActivity(), LoginFloatingWindow.class);
                            finish();
                        });
                break;
            /*设置end*/

            /*订单详情start*/
            case R.id.ivOrderInfoBack:
                //返回订单列表
                if (MyApplication.saleUniqueType == 1) {
                    MyApplication.type = 1;
                } else {
                    MyApplication.type = 2;
                }
                fragmentControl();
                break;
            case R.id.ivOrderInfoQuestion:
                //优惠券问题
                DiscountAmountPopup.showDialog(this, discountList, ivOrderInfoQuestion);
                break;
            case R.id.tvOrderInfoRefund:
                //退款
                if ((realTotal - refundTotal) > 0) {
                    showPopRefund();
                }
                break;
            /*订单详情end*/
            /*收银结果start*/
            case R.id.ivCashierResultBack:
                //返回
            case R.id.tvCashierResultBack:
                //返回收银台
                if (cashierStatus != 0) {
                    linCashierResult.setVisibility(View.GONE);
                }
                break;
            case R.id.ivCashierResultClose:
                //最小化窗口
            case R.id.tvCashierResultClose:
                //关闭窗口
                GT.startFloatingWindow(this, CashierFloatingWindow.class);
                finish();
                break;
            case R.id.tvCashierResultCancel:
                //取消收银
                if (cashierStatus == 0) {
                    IPopupWindow.showDialog(this,
                            DensityUtils.getFloatingWindowWidth(getWidth()),
                            DensityUtils.getFloatingWindowHeight(getHeight()),
                            mV, "确认取消付款？", "确认", () -> {
                                postCancelPay();
                            });
                }
                break;
            /*收银结果end*/
            case R.id.linLoading:
                //加载等待筐
//                linLoading.setVisibility(View.GONE);
                break;
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        cancelTimer();
    }

    /**
     * 支付方式
     */
    private void initDataPayType() {
        orderList.clear();

//        //支付方式：2-支付宝,3-微信,8-金圈支付,13-云闪付支付
//        orderTypeList.clear();
//        orderTypeList.add(new ConditionData(0, "全部"));
//        orderTypeList.add(new ConditionData(2, "支付宝"));
//        orderTypeList.add(new ConditionData(3, "微信"));
//        orderTypeList.add(new ConditionData(8, "金圈支付"));
//        orderTypeList.add(new ConditionData(13, "云闪付支付"));

        //销售订单订单状态：2-未付款,3-已完成
        orderStatusList.clear();
        orderStatusList.add(new ConditionData(0, "全部"));
        orderStatusList.add(new ConditionData(4, "已取消"));
        orderStatusList.add(new ConditionData(5, "支付失败"));
        orderStatusList.add(new ConditionData(2, "未付款"));
        orderStatusList.add(new ConditionData(3, "已完成"));

        //退款订单订单状态：1-未退款,2-已退款
        refundStatusList.clear();
        refundStatusList.add(new ConditionData(0, "全部"));
        refundStatusList.add(new ConditionData(1, "未退款"));
        refundStatusList.add(new ConditionData(2, "已退款"));
    }

    /**************************左侧start*************************/

    /**
     * 控制fragment的变化
     */
    public void fragmentControl() {
        Log.e(tag, "type = " + MyApplication.type + "  index = " + currentTabIndex);
//        if (currentTabIndex != MyApplication.type) {
//            removeBottomColor();
//            setBottomColor();
//            currentTabIndex = MyApplication.type;
//        }
        removeBottomColor();
        setBottomColor();
        currentTabIndex = MyApplication.type;
    }

    /**
     * 设置底部栏按钮变色
     */
    private void setBottomColor() {
        switch (MyApplication.type) {
            case 0:
                lin0.setBackgroundResource(R.drawable.shape_white_left_22);
                iv0.setImageResource(R.mipmap.ic_tab_cashier001);
                tv0.setTextColor(getResources().getColor(R.color.green));
                linCashier.setVisibility(View.VISIBLE);
                linRefund.setVisibility(View.GONE);
                linOrder.setVisibility(View.GONE);
                linTotal.setVisibility(View.GONE);
                linSetting.setVisibility(View.GONE);
                linOrderInfo.setVisibility(View.GONE);
                Log.e(tag, "money = " + MyApplication.cashierMoney);
                if (!TextUtils.isEmpty(MyApplication.cashierMoney)) {
                    isMoney = false;
                    ivCashierMoney.setVisibility(View.GONE);
                    ivCashierCode.setVisibility(View.VISIBLE);
                    cashierMoney = Double.parseDouble(MyApplication.cashierMoney);
                    tvCashierMoney.setText(MyApplication.cashierMoney);
                    cashierKeyBoardView.setResultStr("");
                } else {
                    isMoney = true;
                    ivCashierMoney.setVisibility(View.VISIBLE);
                    ivCashierCode.setVisibility(View.GONE);
                }
                //小窗口扫码
                if (!TextUtils.isEmpty(MyApplication.barCode)) {
                    cashierCode = MyApplication.barCode;
                    MyApplication.barCode = "";
                    etCashierCode.setText(cashierCode);
                    cashierKeyBoardView.setResultStr(cashierCode);
                    //扫码成功后是否立即支付
                    if (!TextUtils.isEmpty(tvCashierMoney.getText().toString().trim())) {
                        cashierMoney = Double.parseDouble(tvCashierMoney.getText().toString().trim());
                    }
                    if (cashierMoney > 0) {
                        isMoney = true;
                        ivCashierMoney.setVisibility(View.VISIBLE);
                        ivCashierCode.setVisibility(View.GONE);
                        postPay();
                    } else {
                        isMoney = false;
                        ivCashierMoney.setVisibility(View.GONE);
                        ivCashierCode.setVisibility(View.VISIBLE);
                    }
                }
                break;
            case 1:
                lin1.setBackgroundResource(R.drawable.shape_white_left_22);
                iv1.setImageResource(R.mipmap.ic_tab_refund001);
                tv1.setTextColor(getResources().getColor(R.color.green));
                linCashier.setVisibility(View.GONE);
                linRefund.setVisibility(View.VISIBLE);
                linOrder.setVisibility(View.GONE);
                linTotal.setVisibility(View.GONE);
                linSetting.setVisibility(View.GONE);
                linOrderInfo.setVisibility(View.GONE);
                break;
            case 2:
                lin2.setBackgroundResource(R.drawable.shape_white_left_22);
                iv2.setImageResource(R.mipmap.ic_tab_order001);
                tv2.setTextColor(getResources().getColor(R.color.green));
                linCashier.setVisibility(View.GONE);
                linRefund.setVisibility(View.GONE);
                linOrder.setVisibility(View.VISIBLE);
                linTotal.setVisibility(View.GONE);
                linSetting.setVisibility(View.GONE);
                linOrderInfo.setVisibility(View.GONE);
                if (TextUtils.isEmpty(orderPayType)) {
                    tvOrderType.setText("收款方式");
                } else {
                    tvOrderType.setText(orderPayType);
                }
                if (MyApplication.orderType == 0) {
                    tvOrderSale.setBackgroundResource(R.drawable.shape_f7_top_5);
                    tvOrderSale.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));
                    tvOrderRefund.setBackgroundResource(0);
                    tvOrderRefund.setTextColor(getResources().getColor(R.color.color_333));
                    linOrderTop.setVisibility(View.VISIBLE);
                    linRefundTop.setVisibility(View.GONE);
                    if (TextUtils.isEmpty(saleListStateName)) {
                        tvOrderStatus.setText("订单状态");
                    } else {
                        tvOrderStatus.setText(saleListStateName);
                    }
                } else {
                    tvOrderRefund.setBackgroundResource(R.drawable.shape_f7_top_5);
                    tvOrderRefund.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));
                    tvOrderSale.setBackgroundResource(0);
                    tvOrderSale.setTextColor(getResources().getColor(R.color.color_333));
                    linOrderTop.setVisibility(View.GONE);
                    linRefundTop.setVisibility(View.VISIBLE);
                    if (TextUtils.isEmpty(retListStateName)) {
                        tvOrderStatus.setText("订单状态");
                    } else {
                        tvOrderStatus.setText(retListStateName);
                    }
                }
                if (currentTabIndex != 5) {
                    page = 1;
                    getOrderList();
                }
                break;
            case 3:
                lin3.setBackgroundResource(R.drawable.shape_white_left_22);
                iv3.setImageResource(R.mipmap.ic_tab_total001);
                tv3.setTextColor(getResources().getColor(R.color.green));
                linCashier.setVisibility(View.GONE);
                linRefund.setVisibility(View.GONE);
                linOrder.setVisibility(View.GONE);
                linTotal.setVisibility(View.VISIBLE);
                linSetting.setVisibility(View.GONE);
                linOrderInfo.setVisibility(View.GONE);
                getDailySummary();
                break;
            case 4:
                lin4.setBackgroundResource(R.drawable.shape_white_left_22);
                iv4.setImageResource(R.mipmap.ic_tab_setting001);
                tv4.setTextColor(getResources().getColor(R.color.green));
                linCashier.setVisibility(View.GONE);
                linRefund.setVisibility(View.GONE);
                linOrder.setVisibility(View.GONE);
                linTotal.setVisibility(View.GONE);
                linSetting.setVisibility(View.VISIBLE);
                linOrderInfo.setVisibility(View.GONE);
                setUISetting();
                break;
            case 5:
                linCashier.setVisibility(View.GONE);
                linRefund.setVisibility(View.GONE);
                linOrder.setVisibility(View.GONE);
                linTotal.setVisibility(View.GONE);
                linSetting.setVisibility(View.GONE);
                linOrderInfo.setVisibility(View.VISIBLE);
                getOrderInfo();
                break;
            default:
                break;
        }
    }

    /**
     * 清除底部栏颜色
     */
    private void removeBottomColor() {
        switch (currentTabIndex) {
            case 0:
                lin0.setBackgroundResource(0);
                iv0.setImageResource(R.mipmap.ic_tab_cashier002);
                tv0.setTextColor(getResources().getColor(R.color.white));
                break;
            case 1:
                lin1.setBackgroundResource(0);
                iv1.setImageResource(R.mipmap.ic_tab_refund002);
                tv1.setTextColor(getResources().getColor(R.color.white));
                break;
            case 2:
                lin2.setBackgroundResource(0);
                iv2.setImageResource(R.mipmap.ic_tab_order002);
                tv2.setTextColor(getResources().getColor(R.color.white));
                break;
            case 3:
                lin3.setBackgroundResource(0);
                iv3.setImageResource(R.mipmap.ic_tab_total002);
                tv3.setTextColor(getResources().getColor(R.color.white));
                break;
            case 4:
                lin4.setBackgroundResource(0);
                iv4.setImageResource(R.mipmap.ic_tab_setting002);
                tv4.setTextColor(getResources().getColor(R.color.white));
                break;
            default:
                break;
        }
    }

    /**
     * 更新UI（设置）
     */
    private void setUISetting() {
        if (MyApplication.isCashierBase) {
            tvSettingCashier.setBackgroundResource(R.drawable.shape_f7_top_5);
            tvSettingCashier.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));
            tvSettingBase.setBackgroundResource(0);
            tvSettingBase.setTextColor(getResources().getColor(R.color.color_333));
            linSettingBase.setVisibility(View.GONE);
            linSettingCashier.setVisibility(View.VISIBLE);
        } else {
            tvSettingBase.setBackgroundResource(R.drawable.shape_f7_top_5);
            tvSettingBase.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));
            tvSettingCashier.setBackgroundResource(0);
            tvSettingCashier.setTextColor(getResources().getColor(R.color.color_333));
            linSettingBase.setVisibility(View.VISIBLE);
            linSettingCashier.setVisibility(View.GONE);
        }
        //基础设置
        if (BaseApplication.getInstance().getUserInfoData() != null) {
            tvShopName.setText(BaseApplication.getInstance().getUserInfoData().getShopName());
            tvBranchName.setText(BaseApplication.getInstance().getUserInfoData().getBranchName());
            tvStaffName.setText(BaseApplication.getInstance().getUserInfoData().getStaffName());
            if (BaseApplication.getInstance().getUserInfoData().getStaffPosition() == 3) {
                linProcess.setVisibility(View.VISIBLE);
            } else {
                linProcess.setVisibility(View.GONE);
            }
        } else {
            linProcess.setVisibility(View.GONE);
        }
        ivStartUp.setSelected(BaseApplication.getInstance().isStartUp());
        ivAuto.setSelected(BaseApplication.getInstance().isAuto());
        ivPayFinish.setSelected(BaseApplication.getInstance().isPayFinish());
        ivRefundFinish.setSelected(BaseApplication.getInstance().isRefundFinish());

        if (SpUtils.getInstance().getBitmap() != null) {
            ivOcr.setImageBitmap(SpUtils.getInstance().getBitmap());
        } else {
            ivOcr.setImageResource(com.yxl.commonlibrary.R.mipmap.ic_default_img);
        }
        if (BaseApplication.getInstance().isProcess()) {
            ivProcess0.setSelected(true);
            ivProcess1.setSelected(false);
        } else {
            ivProcess1.setSelected(true);
            ivProcess0.setSelected(false);
        }
    }

    /**************************左侧end*************************/

    /**************************汇总end*************************/
    /**
     * 清除按钮样式
     */
    private void clearTotalTextBg() {
        tvDay0.setBackgroundResource(R.drawable.shape_e1e2e3_kuang_5);
        tvDay0.setTextColor(getResources().getColor(R.color.color_333));
        tvDay1.setBackgroundResource(R.drawable.shape_e1e2e3_kuang_5);
        tvDay1.setTextColor(getResources().getColor(R.color.color_333));
        tvDay2.setBackgroundResource(R.drawable.shape_e1e2e3_kuang_5);
        tvDay2.setTextColor(getResources().getColor(R.color.color_333));
        tvDay3.setBackgroundResource(R.drawable.shape_e1e2e3_kuang_5);
        tvDay3.setTextColor(getResources().getColor(R.color.color_333));
    }

    /**************************汇总end*************************/

    /**
     * 设置适配器
     */
    private void setAdapter() {
        //订单列表
        rvOrder.setLayoutManager(new LinearLayoutManager(this));
        orderAdapter = new OrderAdapter(this);
        rvOrder.setAdapter(orderAdapter);
        orderAdapter.setOnItemClickListener((view, position) -> {
//            //退款订单无详情
//            if (MyApplication.orderType == 0) {
//                MyApplication.saleUniqueType = 0;
//                MyApplication.saleListUnique = orderList.get(position).getSaleListUnique();
//                MyApplication.type = 5;
//                fragmentControl();
//
////                //测试收银状态
////                MyApplication.saleListUnique = orderList.get(position).getSaleListUnique();
////                GT.startFloatingWindow(GT.getActivity(), CashierResultFloatingWindow.class);
////                finish();
//            }
            MyApplication.saleUniqueType = 0;
            MyApplication.saleListUnique = orderList.get(position).getSaleListUnique();
            MyApplication.type = 5;
            fragmentControl();
        });
        srlOrder.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page++;
                getOrderList();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                getOrderList();
            }
        });

        //汇总列表
        rvTotal.setLayoutManager(new LinearLayoutManager(this));
        totalAdapter = new TotalAdapter(this);
        rvTotal.setAdapter(totalAdapter);
        totalAdapter.setOnItemClickListener((view, position) -> {

        });
        srlTotal.setOnRefreshListener(refreshLayout -> {
            getDailySummary();
        });
        srlTotal.setEnableLoadMore(false);

        //订单详情-退款信息
        rvOrderInfo.setLayoutManager(new LinearLayoutManager(this));
        orderInfoAdapter = new RefundAdapter(this);
        rvOrderInfo.setAdapter(orderInfoAdapter);
        orderInfoAdapter.setOnItemClickListener((view, position) -> {

        });
        srlOrderInfo.setOnRefreshListener(refreshLayout -> {
            getOrderInfo();
        });
        srlOrderInfo.setEnableLoadMore(false);
    }

    /**
     * 退款（popup）
     */
    private void showPopRefund() {
        RefundPopup.showDialog(this,
                DensityUtils.getFloatingWindowWidth(getWidth()),
                DensityUtils.getFloatingWindowHeight(getHeight()),
                mV, realTotal - refundTotal, money -> {
                    if (BaseApplication.getInstance().isProcess()) {
                        showPopRefundApply(money);
                    } else {
                        refundMoney = money;
                        refundPwd = "";
                        postRefund();
                    }
                });
    }

    /**
     * 退款审批（popup）
     *
     * @param money
     */
    private void showPopRefundApply(double money) {
        RefundApplyPopup.showDialog(this,
                DensityUtils.getFloatingWindowWidth(getWidth()),
                DensityUtils.getFloatingWindowHeight(getHeight()),
                mV, pwd -> {
                    refundMoney = money;
                    refundPwd = pwd;
                    postRefund();
                });
    }

    /**
     * 退款结果（popup）
     *
     * @param data
     */
    private void showPopRefundResult(RefundData data, String msg) {
        RefundResultPopup.showDialog(MainFloatingWindow.this,
                DensityUtils.getFloatingWindowWidth(getWidth()),
                DensityUtils.getFloatingWindowHeight(getHeight()),
                mV, data, msg, new RefundResultPopup.MyListener() {
                    @Override
                    public void onRefund() {
                        showPopRefund();
                    }

                    @Override
                    public void onClose() {
                        GT.startFloatingWindow(MainFloatingWindow.this, CashierFloatingWindow.class);
                        finish();
                    }
                });
    }

    private int cashierStatus;//收银状态 0.支付中 1.支付成功 2.支付失败

    /**
     * 更新UI（收银结果）
     *
     * @param data
     */
    private void setUICashierResult(PayData data) {
        if (data == null) {
            showToast(1, "收银失败");
            linCashierResult.setVisibility(View.GONE);
            return;
        }
        MyApplication.saleListUnique = data.getSaleListUnique();
        if (TextUtils.isEmpty(data.getPayStatus())) {
            showToast(1, "收银失败");
            linCashierResult.setVisibility(View.GONE);
            return;
        }
        //INIT-未支付,DOING-支付中，SUCCESS-支付成功，FAIL-支付失败，CLOSE-订单已关闭，CANCEL-订单已取消
        switch (data.getPayStatus()) {
            case "SUCCESS":
                //判断是否结算完成最小化
                cancelTimer();
                clearCashierMoney();
                linCashierResult.setVisibility(View.VISIBLE);
                cashierStatus = 1;
                systemTTS.playText("收银成功" + data.getActualAmount() + "元");
                tvCashierResultTitle.setText("收银成功");
                ivCashierResultStatus.setImageResource(R.mipmap.ic_status001);
                tvCashierResultStatus.setText("收银成功");
                tvCashierResultStatus.setTextColor(getResources().getColor(R.color.green));
                tvCashierResultMoney.setVisibility(View.VISIBLE);
                tvCashierResultMoney.setText("实收金额：￥" + DFUtils.getNum2(data.getActualAmount()));
                if (data.getDiscountAmount() > 0) {
                    tvCashierResultDiscount.setVisibility(View.VISIBLE);
                    tvCashierResultDiscount.setText("优惠金额：￥" + DFUtils.getNum2(data.getDiscountAmount()));
                } else {
                    tvCashierResultDiscount.setVisibility(View.GONE);
                }
                tvCashierResultTips.setVisibility(View.GONE);
                if (BaseApplication.getInstance().isPayFinish()) {
                    tvCashierResultDownTimer.setVisibility(View.VISIBLE);
                    time();
                } else {
                    tvCashierResultDownTimer.setVisibility(View.GONE);
                }
                tvCashierResultCancel.setVisibility(View.GONE);
                tvCashierResultClose.setVisibility(View.VISIBLE);
                tvCashierResultBack.setVisibility(View.VISIBLE);
                break;
            case "DOING":
                //收银中-每2s轮巡一次接口
                linCashierResult.setVisibility(View.VISIBLE);
                cashierStatus = 0;
                tvCashierResultTitle.setText("收银中...");
                Glide.with(GT.getActivity())
                        .asGif()
                        .load(R.drawable.cashiering)
                        .into(ivCashierResultStatus);
                tvCashierResultStatus.setText("收银中");
                tvCashierResultStatus.setTextColor(getResources().getColor(R.color.color_999));
                tvCashierResultMoney.setVisibility(View.VISIBLE);
                tvCashierResultMoney.setText("应收金额：￥" + DFUtils.getNum2(data.getTotalAmount()));
                tvCashierResultDiscount.setVisibility(View.GONE);
                tvCashierResultTips.setVisibility(View.VISIBLE);
                tvCashierResultTips.setVisibility(View.VISIBLE);
                tvCashierResultTips.setText("若由于网络异常等原因导致支付时间较长，\n您可以点击“关闭窗口”按钮，后续可在订单中查看支付结果");
                tvCashierResultDownTimer.setVisibility(View.GONE);
                tvCashierResultCancel.setVisibility(View.VISIBLE);
                tvCashierResultClose.setVisibility(View.VISIBLE);
                tvCashierResultBack.setVisibility(View.GONE);
                new Handler(Looper.getMainLooper()).postDelayed(this::getPayResult, 2000);
                break;
            case "INIT":
            case "FAIL":
            case "CLOSE":
            case "CANCEL":
                setUICashierResultFail(data.getMsg());
                break;
//            case "FAIL":
//                setUICashierResultFail("支付失败");
//                break;
//            case "CLOSE":
//                setUICashierResultFail("订单已关闭");
//                break;
//            case "CANCEL":
//                setUICashierResultFail("订单已取消");
//                break;
            default:
                cancelTimer();
                linCashierResult.setVisibility(View.GONE);
                showToast(1, "收银失败");
                break;
        }
    }

    /**
     * 更新UI（收银结果-失败）
     *
     * @param tips
     */
    private void setUICashierResultFail(String tips) {
        cancelTimer();
        linCashierResult.setVisibility(View.VISIBLE);
        cashierStatus = 2;
        systemTTS.playText("收银失败" + tips);
        tvCashierResultTitle.setText("收银失败");
        ivCashierResultStatus.setImageResource(R.mipmap.ic_status002);
        tvCashierResultStatus.setText("收银失败");
        tvCashierResultStatus.setTextColor(getResources().getColor(R.color.red));
        tvCashierResultMoney.setVisibility(View.GONE);
        tvCashierResultDiscount.setVisibility(View.GONE);
        tvCashierResultTips.setVisibility(View.VISIBLE);
        tvCashierResultTips.setText("失败原因：\n" + tips);
        tvCashierResultDownTimer.setVisibility(View.GONE);
        tvCashierResultCancel.setVisibility(View.GONE);
        tvCashierResultClose.setVisibility(View.VISIBLE);
        tvCashierResultBack.setVisibility(View.VISIBLE);
    }

    /**
     * 清除收银金额
     */
    private void clearCashierMoney() {
        MyApplication.cashierMoney = "";
        cashierMoney = 0;
        cashierCode = "";
        cashierKeyBoardView.setResultStr("");
        tvCashierMoney.setText("");
        etCashierCode.setText("");
    }

    /**************************倒计时start**************************/
    //倒计时
    private CountDownTimer countdown;

    /**
     * 倒计时（弹出退出登录弹窗）
     */
    public void time() {
        cancelTimer();
        if (countdown == null) {
            countdown = new CountDownTimer(Constants.finish_time * 1000L, 1000L) {
                @SuppressLint("SetTextI18n")
                @Override
                public void onTick(long millisUntilFinished) {
                    if (tvCashierResultDownTimer != null) {
                        tvCashierResultDownTimer.setText((millisUntilFinished / 1000) + "秒后关闭窗口");
                    }
                }

                @Override
                public void onFinish() {
                    //倒计时结束时触发
                    GT.startFloatingWindow(MainFloatingWindow.this, CashierFloatingWindow.class);
                    finish();
                }
            };
            countdown.start();
        } else {
            countdown.start();
        }
    }

    /**
     * 计时器清除（没暂停）
     */
    public void cancelTimer() {
        if (countdown != null) {
            countdown.cancel();
        }
    }

    /**************************倒计时end**************************/

    /**
     * 显示加载框
     */
    private void showDialog() {
        if (linLoading.getVisibility() != View.VISIBLE) {
            linLoading.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 隐藏加载框
     */
    private void hideDialog() {
        if (linLoading.getVisibility() == View.VISIBLE) {
            linLoading.setVisibility(View.GONE);
        }
    }

    /**
     * 吐司
     *
     * @param type 0.正确 1.错误
     * @param msg
     */
    private void showToast(int type, String msg) {
        if (type == 0) {
            linToast.setBackgroundResource(R.drawable.shape_green_5);
            ivToast.setImageResource(R.mipmap.ic_toast001);
        } else {
            linToast.setBackgroundResource(R.drawable.shape_red_5);
            ivToast.setImageResource(R.mipmap.ic_toast002);
        }
        tvToast.setText(msg);
        ViewUtils.fadeInAndFadeOut(linToast);
    }

    /**
     * 获取收款方式
     */
    private void getPaymentTypeList() {
        if (!NetWorkUtils.isConnect(this)) {
            showToast(1, "无网络连接");
            return;
        }
        RXHttpUtil.requestByGetAsResponse(this, ZURL.getPaymentTypeList(), new HashMap<>(), PaymentTypeData.class,
                new RequestListener<PaymentTypeData>() {
                    @Override
                    public void success(PaymentTypeData data) {
                        if (data == null) {
                            return;
                        }
                        if (data.getList() == null) {
                            return;
                        }
                        orderTypeList.clear();
                        orderTypeList.add(new ConditionData(0, "全部"));
                        orderTypeList.addAll(data.getList());
                    }
                });
    }

    /**
     * 收银
     */
    private void postPay() {
        if (!NetWorkUtils.isConnect(this)) {
            showToast(1, "无网络连接");
            return;
        }
        if (cashierMoney > Constants.cashier_max) {
            showToast(1, "收银金额不能大于" + Constants.cashier_max + "元");
            return;
        }
        if (cashierMoney < Constants.cashier_min) {
            showToast(1, "收银金额不能小于" + Constants.cashier_min + "元");
            return;
        }
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("saleListUnique", StringUtils.getOrderId());
        map.put("totalAmount", cashierMoney);
        map.put("authCode", cashierCode);
        RXHttpUtil.requestByBodyPostAsResponse(this, ZURL.getPay(), map, PayData.class, new RequestListener<PayData>() {
            @Override
            public void success(PayData data) {
                hideDialog();
//                MyApplication.cashierMoney = "";
//                cashierMoney = 0;
//                cashierCode = "";
//                cashierKeyBoardView.setResultStr("");
//                tvCashierMoney.setText("");
//                etCashierCode.setText("");
//                MyApplication.saleListUnique = data.getSaleListUnique();
//                GT.startFloatingWindow(GT.getActivity(), CashierResultFloatingWindow.class);
//                finish();
                setUICashierResult(data);
            }

            @Override
            public void onError(String msg, String code) {
                if (!TextUtils.isEmpty(code)) {
                    if (code.equals("401")) {
//                        BaseApplication.getInstance().saveUserInfo("");
//                        GT.startFloatingWindow(GT.getActivity(), LoginFloatingWindow.class);
//                        finish();
                        postLogin(0);
                    } else {
                        hideDialog();
                        showToast(1, msg);
                        systemTTS.playText("收银失败" + msg);
                    }
                }
            }
        });
    }

    /**
     * 订单列表
     */
    private void getOrderList() {
        if (!NetWorkUtils.isConnect(this)) {
            showToast(1, "无网络连接");
            return;
        }
        showDialog();
        showSoftKeyboard(etOrderSearch, false);
        String url;
        Map<String, Object> map = new HashMap<>();
        map.put("pageIndex", page);
        map.put("pageSize", Constants.limit);
        if (saleListPayment != 0) {
            map.put("paymentMethod", saleListPayment);
        }
        if (MyApplication.orderType == 1) {
            url = ZURL.getRefundList();
            map.put("retListUnique", saleListUnique);
            if (retListState != 0) {
                map.put("retListState", retListState);
            }
            map.put("retListDatetimeList", orderStartTime + "," + orderEndTime);
        } else {
            url = ZURL.getSaleList();
            map.put("saleListUnique", saleListUnique);
            if (saleListState != 0) {
                //payStatus;//CANCEL-已取消，DOING-未付款，SUCCESS-已支付，FAIL-支付失败
                //销售订单订单状态：2-未付款,3-已完成 4.已取消 5.支付失败
                switch (saleListState) {
                    case 2:
                        payStatus = "DOING";
                        break;
                    case 3:
                        payStatus = "SUCCESS";
                        break;
                    case 4:
                        payStatus = "CANCEL";
                        break;
                    case 5:
                        payStatus = "FAIL";
                        break;
                    default:
                        payStatus = "";
                        break;
                }
                map.put("payStatus", payStatus);
            }
            map.put("saleListDatetimeList", orderStartTime + "," + orderEndTime);
        }
        RXHttpUtil.requestByGetAsResponse(this, url, map, OrderListData.class, new RequestListener<OrderListData>() {
            @Override
            public void success(OrderListData data) {
                hideDialog();
                if (page == 1) {
                    srlOrder.finishRefresh();
                    orderList.clear();
                } else {
                    srlOrder.finishLoadMore();
                }
                orderList.addAll(data.getRows());
                if (orderList.size() > 0) {
                    rvOrder.setVisibility(View.VISIBLE);
                    linOrderEmpty.setVisibility(View.GONE);
                    orderAdapter.setDataList(orderList);
                } else {
                    rvOrder.setVisibility(View.GONE);
                    linOrderEmpty.setVisibility(View.VISIBLE);
                }
            }

            @Override
            public void onError(String msg, String code) {
                if (page == 1) {
                    srlOrder.finishRefresh();
                } else {
                    srlOrder.finishLoadMore();
                }
                if (orderList.size() > 0) {
                    rvOrder.setVisibility(View.VISIBLE);
                    linOrderEmpty.setVisibility(View.GONE);
                } else {
                    rvOrder.setVisibility(View.GONE);
                    linOrderEmpty.setVisibility(View.VISIBLE);
                }
                if (!TextUtils.isEmpty(code)) {
                    if (code.equals("401")) {
//                        BaseApplication.getInstance().saveUserInfo("");
//                        GT.startFloatingWindow(GT.getActivity(), LoginFloatingWindow.class);
//                        finish();
                        postLogin(1);
                    } else {
                        hideDialog();
                        showToast(1, msg);
                    }
                }
            }
        });
    }

    /**
     * 查询日统计信息
     */
    private void getDailySummary() {
        if (!NetWorkUtils.isConnect(this)) {
            showToast(1, "无网络连接");
            return;
        }
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("startTime", totalStartTime);
        map.put("endTime", totalEndTime);
        RXHttpUtil.requestByBodyPostAsResponse(this, ZURL.getDailySummary(), map, DailySummaryData.class, new RequestListener<DailySummaryData>() {
            @Override
            public void success(DailySummaryData data) {
                hideDialog();
                srlTotal.finishRefresh();
                if (data.getTotal() != null) {
                    tvTotalRealTotal.setText(DFUtils.getNum2(data.getTotal().getActualReceiptAmount()));
                    tvTotalTotal.setText(DFUtils.getNum2(data.getTotal().getTransactionAmount()));
                    tvTotalDiscount.setText(DFUtils.getNum2(data.getTotal().getDiscountAmount()));
                    tvTotalRefund.setText(DFUtils.getNum2(data.getTotal().getRefundAmount()));
                }
                totalList.clear();
                totalList.addAll(data.getPayTypeList());
                if (totalList.size() > 0) {
                    rvTotal.setVisibility(View.VISIBLE);
                    linTotalEmpty.setVisibility(View.GONE);
                    totalAdapter.setDataList(totalList);
                } else {
                    rvTotal.setVisibility(View.GONE);
                    linTotalEmpty.setVisibility(View.VISIBLE);
                }
            }

            @Override
            public void onError(String msg, String code) {
                srlTotal.finishRefresh();
                if (totalList.size() > 0) {
                    rvTotal.setVisibility(View.VISIBLE);
                    linTotalEmpty.setVisibility(View.GONE);
                } else {
                    rvTotal.setVisibility(View.GONE);
                    linTotalEmpty.setVisibility(View.VISIBLE);
                }
                if (!TextUtils.isEmpty(code)) {
                    if (code.equals("401")) {
//                        BaseApplication.getInstance().saveUserInfo("");
//                        GT.startFloatingWindow(GT.getActivity(), LoginFloatingWindow.class);
//                        finish();
                        postLogin(2);
                    } else {
                        hideDialog();
                        showToast(1, msg);
                    }
                }
            }
        });
    }

    /**
     * 订单详情（判断订单是否存在）
     */
    private void getOrderInfoConfirm() {
        if (!NetWorkUtils.isConnect(this)) {
            showToast(1, "无网络连接");
            return;
        }
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("tradeNo", refundCode);
        RXHttpUtil.requestByGetAsResponse(this, ZURL.getSaleInfoNo(), map, OrderInfoData.class, new RequestListener<OrderInfoData>() {
            @Override
            public void success(OrderInfoData data) {
                hideDialog();
                MyApplication.saleUniqueType = 1;
                MyApplication.saleListUnique = refundCode;
                MyApplication.type = 5;
                fragmentControl();
            }

            @Override
            public void onError(String msg, String code) {
                if (!TextUtils.isEmpty(code)) {
                    if (code.equals("401")) {
//                        BaseApplication.getInstance().saveUserInfo("");
//                        GT.startFloatingWindow(GT.getActivity(), LoginFloatingWindow.class);
//                        finish();
                        postLogin(3);
                    } else {
                        hideDialog();
                        showToast(1, msg);
                    }
                }
            }
        });
    }

    /**
     * 订单详情
     */
    private void getOrderInfo() {
        if (!NetWorkUtils.isConnect(this)) {
            showToast(1, "无网络连接");
            return;
        }
        if (TextUtils.isEmpty(MyApplication.saleListUnique)) {
            showToast(1, "暂无订单编号");
            fragmentControl();
            if (MyApplication.saleUniqueType == 1) {
                MyApplication.type = 1;
            } else {
                MyApplication.type = 2;
            }
            fragmentControl();
            return;
        }
        showDialog();
        String url;
        Map<String, Object> map = new HashMap<>();
        if (MyApplication.saleUniqueType == 1) {
            url = ZURL.getSaleInfoNo();
            map.put("tradeNo", MyApplication.saleListUnique);
        } else {
            url = ZURL.getSaleInfo();
            map.put("saleListUnique", MyApplication.saleListUnique);
        }
        RXHttpUtil.requestByGetAsResponse(this, url, map, OrderInfoData.class, new RequestListener<OrderInfoData>() {
            @Override
            public void success(OrderInfoData data) {
                hideDialog();
                srlOrderInfo.finishRefresh();
                MyApplication.saleListUnique = data.getSaleListUnique();
                total = data.getSaleListTotal();
                realTotal = data.getSaleListActuallyReceived();
                refundTotal = data.getRetTotalAmount();
                /*付款方式：1.现金 8.小程序 13.金圈*/
                tvOrderInfoPayType.setText(data.getPaymentMethod());
                switch (data.getPaymentMethodCode()) {
                    case 1:
                        ivOrderInfoPayType.setImageResource(R.mipmap.ic_payment001);
//                        tvOrderInfoPayType.setText("现金");
                        break;
                    case 8:
                        ivOrderInfoPayType.setImageResource(R.mipmap.ic_payment002);
//                        tvOrderInfoPayType.setText("小程序");
                        break;
                    case 13:
                        ivOrderInfoPayType.setImageResource(R.mipmap.ic_payment003);
//                        tvOrderInfoPayType.setText("金圈");
                        break;
                    default:
                        ivOrderInfoPayType.setImageResource(0);
//                        tvOrderInfoPayType.setText("");
                        break;
                }
                if (TextUtils.isEmpty(data.getPayStatus())) {
                    tvOrderInfoStatus.setText("");
                } else {
                    switch (data.getPayStatus()) {
                        case "SUCCESS":
                            tvOrderInfoStatus.setText("已完成");
                            tvOrderInfoStatus.setTextColor(getResources().getColor(R.color.green));
                            break;
                        case "DOING":
                            tvOrderInfoStatus.setText("未付款");
                            tvOrderInfoStatus.setTextColor(getResources().getColor(R.color.color_666));
                            break;
                        case "CANCEL":
                            tvOrderInfoStatus.setText("已取消");
                            tvOrderInfoStatus.setTextColor(getResources().getColor(R.color.red));
                            break;
                        case "FAIL":
                            tvOrderInfoStatus.setText("支付失败");
                            tvOrderInfoStatus.setTextColor(getResources().getColor(R.color.red));
                            break;
                        default:
                            tvOrderInfoStatus.setText("");
                            break;
                    }
                }

                //退款按钮是否显示：收款方式为金圈（13）、订单来源为插件（“2”）、订单状态为已完成（“SUCCESS”）、退款金额小于实收金额
                if (TextUtils.isEmpty(data.getSourceType()) || TextUtils.isEmpty(data.getPayStatus())) {
                    tvOrderInfoRefund.setVisibility(View.GONE);
                } else {
                    if (data.getPaymentMethodCode() == 13
                            && data.getSourceType().equals("2")
                            && data.getPayStatus().equals("SUCCESS")
                            && (refundTotal < realTotal)) {
                        tvOrderInfoRefund.setVisibility(View.VISIBLE);
                    } else {
                        tvOrderInfoRefund.setVisibility(View.GONE);
                    }
                }
//                if (data.getPaymentMethodCode() == 13) {
//                    if (TextUtils.isEmpty(data.getSourceType())) {
//                        tvOrderInfoRefund.setVisibility(View.GONE);
//                    } else {
//                        if (data.getSourceType().equals("2")) {
//                            tvOrderInfoRefund.setVisibility(View.VISIBLE);
//                        } else {
//                            tvOrderInfoRefund.setVisibility(View.GONE);
//                        }
//                    }
//                } else {
//                    tvOrderInfoRefund.setVisibility(View.GONE);
//                }

                tvOrderInfoTotal.setText("￥" + DFUtils.getNum2(data.getSaleListTotal()));
                discountAmount = data.getDiscountAmount();//优惠金额
                discountList.clear();
                if (data.getDiscountList() == null) {
                    ivOrderInfoQuestion.setVisibility(View.GONE);
                } else {
                    if (data.getDiscountList().size() > 0) {
                        ivOrderInfoQuestion.setVisibility(View.VISIBLE);
                        discountList.addAll(data.getDiscountList());
                    } else {
                        ivOrderInfoQuestion.setVisibility(View.GONE);
                    }
                }
                tvOrderInfoDisCount.setText("￥" + DFUtils.getNum2(discountAmount));
                tvOrderInfoRealTotal.setText("￥" + DFUtils.getNum2(data.getSaleListActuallyReceived()));

                /*订单信息*/
                if (TextUtils.isEmpty(data.getSaleListUnique())) {
                    tvOrderInfoNo.setText("--");
                } else {
                    tvOrderInfoNo.setText(data.getSaleListUnique());
                }
                if (TextUtils.isEmpty(data.getEquipmentSourceType())) {
                    tvOrderInfoSource.setText("--");
                } else {
                    tvOrderInfoSource.setText(data.getEquipmentSourceType());
                }
                if (TextUtils.isEmpty(data.getTradeNo())) {
                    tvOrderInfoTradeNo.setText("--");
                } else {
                    tvOrderInfoTradeNo.setText(data.getTradeNo());
                }
                if (TextUtils.isEmpty(data.getPayTime())) {
                    tvOrderInfoPayTime.setText("--");
                } else {
                    tvOrderInfoPayTime.setText(data.getPayTime());
                }
                if (TextUtils.isEmpty(data.getCusName())) {
                    tvCusName.setText("--");
                } else {
                    tvCusName.setText(data.getCusName());
                }
                if (TextUtils.isEmpty(data.getCusPhone())) {
                    tvCusPhone.setText("--");
                } else {
                    tvCusPhone.setText(data.getCusPhone());
                }

                /*退款信息*/
                if (data.getReturnList() == null) {
                    linOrderInfoRefund.setVisibility(View.GONE);
                } else {
                    if (data.getReturnList().size() > 0) {
                        linOrderInfoRefund.setVisibility(View.VISIBLE);
                        tvOrderInfoRefundTotal.setText("￥" + DFUtils.getNum2(data.getRetTotalAmount()));
                        orderInfoList.clear();
                        orderInfoList.addAll(data.getReturnList());
                        orderInfoAdapter.setDataList(orderInfoList);
                    } else {
                        linOrderInfoRefund.setVisibility(View.GONE);
                    }
                }
            }

            @Override
            public void onError(String msg, String code) {
                srlOrderInfo.finishRefresh();
                if (!TextUtils.isEmpty(code)) {
                    if (code.equals("401")) {
//                        BaseApplication.getInstance().saveUserInfo("");
//                        GT.startFloatingWindow(GT.getActivity(), LoginFloatingWindow.class);
//                        finish();
                        postLogin(4);
                    } else {
                        hideDialog();
                        showToast(1, msg);
                        if (MyApplication.saleUniqueType == 1) {
                            MyApplication.type = 1;
                        } else {
                            MyApplication.type = 2;
                        }
                        fragmentControl();
                    }
                } else {
                    hideDialog();
                    showToast(1, msg);
                    if (MyApplication.saleUniqueType == 1) {
                        MyApplication.type = 1;
                    } else {
                        MyApplication.type = 2;
                    }
                    fragmentControl();
                }
            }
        });
    }

    private double refundMoney;
    private String refundPwd;

    /**
     * 退款
     */
    private void postRefund() {
        if (!NetWorkUtils.isConnect(this)) {
            showToast(1, "无网络连接");
            return;
        }
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("saleListUnique", MyApplication.saleListUnique);
        map.put("refundAmount", refundMoney);
        if (BaseApplication.getInstance().isProcess()) {
            map.put("refundPassword", refundPwd);
        }
        RXHttpUtil.requestByBodyPostAsResponse(this, ZURL.getRefund(), map, RefundData.class, new RequestListener<RefundData>() {
            @Override
            public void success(RefundData data) {
                hideDialog();
                MyApplication.saleListUnique = data.getSaleListUnique();
                refundCode = "";
                refundKeyBoardView.setResultStr("");
                etRefundCode.setText("");
                showPopRefundResult(data, "");
                getOrderInfo();
            }

            @Override
            public void onError(String msg, String code) {
                if (!TextUtils.isEmpty(code)) {
                    if (code.equals("401")) {
//                        showToast(1, msg);
//                        BaseApplication.getInstance().saveUserInfo("");
//                        GT.startFloatingWindow(GT.getActivity(), LoginFloatingWindow.class);
//                        finish();
                        postLogin(5);
                    } else {
                        hideDialog();
                        showPopRefundResult(null, msg);
                    }
                }
            }
        });
    }

    private String settingPwd;
    private int needAudit;

    /**
     * 保存配置
     */
    private void postSetting() {
        if (!NetWorkUtils.isConnect(this)) {
            showToast(1, "无网络连接");
            return;
        }
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("needAudit", needAudit);
        if (needAudit == 1) {
            map.put("refundPassword", settingPwd);
        }
        RXHttpUtil.requestByBodyPostAsResponse(this, ZURL.getSettingSave(), map, String.class, new RequestListener<String>() {
            @Override
            public void success(String s) {
                hideDialog();
                if (needAudit == 1) {
                    ivProcess0.setSelected(true);
                    ivProcess1.setSelected(false);
                    SpUtils.getInstance().put(Constants.process, Constants.process);
                } else {
                    ivProcess1.setSelected(true);
                    ivProcess0.setSelected(false);
                    SpUtils.getInstance().put(Constants.process, "");
                }
            }

            @Override
            public void onError(String msg, String code) {
                if (needAudit == 1) {
                    ivProcess0.setSelected(false);
                    ivProcess1.setSelected(true);
                } else {
                    ivProcess0.setSelected(true);
                    ivProcess1.setSelected(false);
                }
                if (!TextUtils.isEmpty(code)) {
                    if (code.equals("401")) {
//                        BaseApplication.getInstance().saveUserInfo("");
//                        GT.startFloatingWindow(GT.getActivity(), LoginFloatingWindow.class);
//                        finish();
                        postLogin(6);
                    } else {
                        hideDialog();
                        showToast(1, msg);
                    }
                }
            }
        });
    }

    /**
     * 支付结果查询
     */
    private void getPayResult() {
        if (!NetWorkUtils.isConnect(this)) {
            showToast(1, "无网络连接");
            return;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("saleListUnique", MyApplication.saleListUnique);
        RXHttpUtil.requestByGetAsResponse(this,
                ZURL.getPayResult(),
                map,
                PayData.class,
                new RequestListener<PayData>() {
                    @Override
                    public void success(PayData data) {
                        setUICashierResult(data);
                    }

                    @Override
                    public void onError(String msg, String code) {
                        if (!TextUtils.isEmpty(code)) {
                            if (code.equals("401")) {
//                                showToast(1, msg);
//                                BaseApplication.getInstance().saveUserInfo("");
//                                GT.startFloatingWindow(GT.getActivity(), LoginFloatingWindow.class);
//                                finish();
                                postLogin(6);
                            } else {
                                setUICashierResultFail(msg);
                            }
                        }
                    }
                });
    }

    /**
     * 撤销订单（取消付款）
     */
    private void postCancelPay() {
        if (!NetWorkUtils.isConnect(this)) {
            showToast(1, "无网络连接");
            return;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("saleListUnique", MyApplication.saleListUnique);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getPayCancel(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        cancelTimer();
                        showToast(1, s);
                        linCashierResult.setVisibility(View.GONE);
                    }

                    @Override
                    public void onError(String msg, String code) {
                        cancelTimer();
                        if (!TextUtils.isEmpty(code)) {
                            if (code.equals("401")) {
//                                BaseApplication.getInstance().saveUserInfo("");
//                                GT.startFloatingWindow(GT.getActivity(), LoginFloatingWindow.class);
//                                finish();
                                postLogin(7);
                            } else {
                                showToast(1, msg);
                            }
                        }
                    }
                });
    }

    /**
     * 登录
     *
     * @param type 0.收银 1.订单列表 2.汇总列表 3.订单详情（判断订单是否存在） 4.订单详情 5.退款 6.收银结果查询 7.取消付款
     */
    private void postLogin(int type) {
        if (!NetWorkUtils.isConnect(this)) {
            hideDialog();
            showToast(1, "无网络连接");
            return;
        }
        String account = BaseApplication.getInstance().getAccount();
        String pwd = BaseApplication.getInstance().getPwd();
        if (TextUtils.isEmpty(account)) {
            goToLogin();
            return;
        }
        if (account.length() < 11) {
            goToLogin();
            return;
        }
        if (TextUtils.isEmpty(pwd)) {
            goToLogin();
            return;
        }
        if (pwd.length() < 6) {
            goToLogin();
            return;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("phone", account);
        map.put("password", pwd);
        RXHttpUtil.requestByBodyPostAsOriginalResponse(this,
                ZURL.getLogin(),
                map,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        Log.e("111111", "登录 = " + s);
                        BaseData data = new Gson().fromJson(s, BaseData.class);
                        if (data == null) {
                            goToLogin();
                            return;
                        }
                        if (data.getCode() != Constants.SUCCESS_CODE) {
                            goToLogin();
                            return;
                        }
                        if (data.getData() == null) {
                            goToLogin();
                            return;
                        }
                        hideDialog();
                        BaseApplication.getInstance().saveUserInfo(s);
                        Log.e(tag, "重新获取token = " + BaseApplication.getInstance().getToken());
                        switch (type) {
                            case 0:
                                postPay();
                                break;
                            case 1:
                                getOrderList();
                                break;
                            case 2:
                                getDailySummary();
                                break;
                            case 3:
                                getOrderInfoConfirm();
                                break;
                            case 4:
                                getOrderInfo();
                                break;
                            case 5:
                                postRefund();
                                break;
                            case 6:
                                getPayResult();
                                break;
                            case 7:
                                postCancelPay();
                                break;
                            default:
                                goToLogin();
                                break;
                        }
                    }

                    @Override
                    public void onError(String msg, String code) {
                        Log.e("111111", "登录 s = " + msg + " code = " + code);
                        goToLogin();
                    }
                });
    }

    /**
     * 跳转到登录页
     */
    private void goToLogin() {
        hideDialog();
        BaseApplication.getInstance().saveUserInfo("");
        GT.startFloatingWindow(GT.getActivity(), LoginFloatingWindow.class);
        finish();
    }

    /**
     * 弹出软键盘
     *
     * @param isShow
     */
    private void showSoftKeyboard(EditText editText, boolean isShow) {
        InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
        if (imm != null) {
            if (isShow) {
                imm.showSoftInput(editText, InputMethodManager.SHOW_IMPLICIT);
            } else {
                imm.hideSoftInputFromWindow(editText.getWindowToken(), 0);
            }
        }
    }

    /************************版本更新start***********************/

    private VersionData versionData;

    /**
     * 更新版本
     */
    private void checkUpgrade() {
        if (!NetWorkUtils.isConnect(this)) {
            showToast(1, "无网络连接");
            return;
        }
        HttpParams map = new HttpParams();
        DownloadBuilder builder = AllenVersionChecker
                .getInstance()
                .requestVersion()
                .setRequestUrl(ZURL.getVersion())
                .setRequestParams(map)
                .request(new RequestVersionListener() {
                    @Nullable
                    @Override
                    public UIData onRequestVersionSuccess(DownloadBuilder downloadBuilder, String result) {
                        Log.e("111111", "version success = " + result);
                        versionData = new Gson().fromJson(result, VersionData.class);
                        if (versionData == null) {
                            showToast(1, result);
                            return null;
                        }
                        if (versionData.getData() == null) {
                            showToast(1, versionData.getMsg());
                            return null;
                        }
                        if (versionData.getCode() != Constants.SUCCESS_CODE) {
                            showToast(1, versionData.getMsg());
                            return null;
                        }
                        //最新版本>当前版本
                        if (versionData.getData().getVersionCode() > PackageUtils.getPackageCode(GT.getActivity())) {
                            GT.startFloatingWindow(MainFloatingWindow.this, CashierFloatingWindow.class);
                            finish();
                            //是否强制更新 0.否 1.是
                            if (versionData.getData().getForceUpgrade() == 1) {
                                downloadBuilder.setForceUpdateListener(() -> {
                                    finish();
                                });
                            }
                            return crateUIData();
                        } else {
                            showToast(1, "当前版本为最新版本");
                            return null;
                        }
                    }

                    @Override
                    public void onRequestVersionFailure(String message) {
                        Log.e("111111", "version error = " + message);
                        showToast(1, message);
                    }
                });
        builder.setNotificationBuilder(createCustomNotification());
//        builder.setDownloadAPKPath(this.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS).getPath() + "/");
        builder.setDownloadAPKPath(FileUtils.getPath(this));
        builder.setCustomVersionDialogListener(createCustomDialog());
        builder.setCustomDownloadingDialogListener(createCustomDownloadingDialog());
        builder.setCustomDownloadFailedListener(createCustomDownloadFailedDialog());
        builder.setForceRedownload(true);
        builder.executeMission(this);
    }

    /**
     * 自定义下载中对话框，下载中会连续回调此方法 updateUI
     * 务必用库传回来的context 实例化你的dialog
     *
     * @return
     */
    private CustomDownloadingDialogListener createCustomDownloadingDialog() {
        return new CustomDownloadingDialogListener() {
            @Override
            public Dialog getCustomDownloadingDialog(Context context, int progress, UIData versionBundle) {
                BaseDialog baseDialog = new BaseDialog(context, R.style.dialog_style, R.layout.dialog_download);
                return baseDialog;
            }

            @Override
            public void updateUI(Dialog dialog, int progress, UIData versionBundle) {
                TextView tvProgress = dialog.findViewById(R.id.tvProgress);
                ProgressBar progressBar = dialog.findViewById(R.id.pb);
                progressBar.setProgress(progress);
                tvProgress.setText(getString(R.string.versionchecklib_progress, progress));
            }
        };
    }

    /**
     * 务必用库传回来的context 实例化你的dialog
     *
     * @return
     */
    private CustomDownloadFailedListener createCustomDownloadFailedDialog() {
        return (context, versionBundle) -> {
            BaseDialog baseDialog = new BaseDialog(context, R.style.dialog_style, R.layout.dialog_download_failed);
            return baseDialog;
        };
    }

    private CustomVersionDialogListener createCustomDialog() {
        return (context, versionBundle) -> {
            BaseDialog dialog = new BaseDialog(context, R.style.dialog_style, R.layout.dialog_download_two);
            dialog.setCancelable(false);

            TextView tvTitle, tvContent;
            tvTitle = dialog.findViewById(R.id.tv_title);
            tvContent = dialog.findViewById(R.id.tv_msg);

            tvTitle.setText(versionBundle.getTitle());
            tvContent.setText(versionBundle.getContent());
            dialog.setCanceledOnTouchOutside(false);
            return dialog;
        };
    }

    private NotificationBuilder createCustomNotification() {
        return NotificationBuilder.create()
                .setRingtone(true)
                .setIcon(R.mipmap.ic_launcher)
                .setTicker("custom_ticker")
                .setContentTitle("金圈收银助手升级")
                .setContentText("升级中");
    }

    /**
     * @return
     * @important 使用请求版本功能，可以在这里设置downloadUrl
     * 这里可以构造UI需要显示的数据
     * UIData 内部是一个Bundle
     */
    private UIData crateUIData() {
        UIData uiData = UIData.create();
        uiData.setTitle(versionData.getData().getIntroduce());
        uiData.setDownloadUrl(versionData.getData().getFileUrl());
        uiData.setContent(versionData.getData().getRemark());
        return uiData;
    }

    /************************版本更新end***********************/

}
