package com.yxl.cashier.helper.view.pickerview;

import android.content.Context;
import android.util.AttributeSet;
import android.util.Log;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.github.gzuliyujiang.wheelpicker.widget.BaseWheelLayout;
import com.github.gzuliyujiang.wheelview.annotation.ScrollState;
import com.github.gzuliyujiang.wheelview.widget.NumberWheelView;
import com.github.gzuliyujiang.wheelview.widget.WheelView;
import com.yxl.cashier.helper.R;

import java.util.Arrays;
import java.util.List;

/**
 * Describe:日期时间滚轮控件
 * Created by jingang on 2022/12/7
 */
public class DateTimeWheelLayout extends BaseWheelLayout {
    private String tag = "DateTimeWheelLayout";
    private NumberWheelView wheelYear, wheelMonth, wheelDay;
    private DateTimeEntity startValue;
    private DateTimeEntity endValue;
    private Integer selectedYear;
    private Integer selectedMonth;
    private Integer selectedDay;
    private OnDateTimeSelectedListener onDateTimeSelectedListener;
    private boolean resetWhenLinkage = true;


    public DateTimeWheelLayout(Context context) {
        super(context);
    }

    public DateTimeWheelLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public DateTimeWheelLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected int provideLayoutRes() {
        return R.layout.wheel_date_time;
    }

    @Override
    protected List<WheelView> provideWheelViews() {
        return Arrays.asList(wheelYear, wheelMonth, wheelDay);
    }

    @Override
    protected void onInit(@NonNull Context context) {
        wheelYear = findViewById(R.id.wheelYear);
        wheelMonth = findViewById(R.id.wheelMonth);
        wheelDay = findViewById(R.id.wheelDay);
    }

    @Override
    protected void onAttributeSet(@NonNull Context context, @Nullable AttributeSet attrs) {
        setDateFormatter(new SimpleDateFormatter());
    }


    @Override
    protected void onVisibilityChanged(@NonNull View changedView, int visibility) {
        super.onVisibilityChanged(changedView, visibility);
//        if (visibility == VISIBLE && startValue == null && endValue == null) {
//            setRange(DateTimeEntity.today(), DateTimeEntity.yearOnFuture(30), DateTimeEntity.today());
//        }
    }

    @Override
    public void onWheelSelected(WheelView view, int position) {
        int id = view.getId();
        if (id == R.id.wheelYear) {
            //年
            selectedYear = wheelYear.getItem(position);
            if (resetWhenLinkage) {
                selectedMonth = null;
                selectedDay = null;
            }
            changeMonth(selectedYear);
            return;
        }
        if (id == R.id.wheelMonth) {
            selectedMonth = wheelMonth.getItem(position);
            if (resetWhenLinkage) {
                selectedDay = null;
            }
            changeDay(selectedYear, selectedMonth);
            return;
        }
        if (id == R.id.wheelDay) {
            selectedDay = wheelDay.getItem(position);
            dateSelectedCallback();
        }
    }

    @Override
    public void onWheelScrollStateChanged(WheelView view, @ScrollState int state) {
        int id = view.getId();
        if (id == R.id.wheelYear) {
            wheelMonth.setEnabled(state == ScrollState.IDLE);
            wheelDay.setEnabled(state == ScrollState.IDLE);
            return;
        }
        if (id == R.id.wheelMonth) {
            wheelYear.setEnabled(state == ScrollState.IDLE);
            wheelDay.setEnabled(state == ScrollState.IDLE);
            return;
        }
        if (id == R.id.wheelDay) {
            wheelMonth.setEnabled(state == ScrollState.IDLE);
            wheelYear.setEnabled(state == ScrollState.IDLE);
        }
    }

    private void dateSelectedCallback() {
        if (onDateTimeSelectedListener == null) {
            return;
        }
        wheelDay.post(() -> onDateTimeSelectedListener.onDateSelected(selectedYear, selectedMonth, selectedDay));
    }

    /**
     * 设置日期时间范围
     */
    public void setRange(DateTimeEntity startValue, DateTimeEntity endValue) {
        setRange(startValue, endValue, null);
    }

    /**
     * 设置日期时间范围
     */
    public void setRange(DateTimeEntity startValue, DateTimeEntity endValue, DateTimeEntity defaultValue) {
        if (startValue == null) {
            return;
        }
        if (endValue == null) {
            return;
        }
        Log.e(tag, "start = " + startValue.getYear() + " end = " + endValue.getYear());
//        if (endValue.toTimeInMillis() < startValue.toTimeInMillis()) {
//            throw new IllegalArgumentException("Ensure the start date is less than the end date");
//        }
        this.startValue = startValue;
        this.endValue = endValue;
        if (defaultValue != null) {
            selectedYear = defaultValue.getYear();
            selectedMonth = defaultValue.getMonth();
            selectedDay = defaultValue.getDay();
        } else {
            selectedYear = null;
            selectedMonth = null;
            selectedDay = null;
        }
        changeYear();
    }

    public void setDefaultValue(DateTimeEntity defaultValue) {
        setRange(startValue, endValue, defaultValue);
    }

    public void setDateFormatter(final DateFormatter dateFormatter) {
        if (dateFormatter == null) {
            return;
        }
        wheelYear.setFormatter(value -> dateFormatter.formatYear((Integer) value));
        wheelMonth.setFormatter(value -> dateFormatter.formatMonth((Integer) value));
        wheelDay.setFormatter(value -> dateFormatter.formatDay((Integer) value));
    }

    public void setOnDateTimeSelectedListener(OnDateTimeSelectedListener onDateTimeSelectedListener) {
        this.onDateTimeSelectedListener = onDateTimeSelectedListener;
    }

    public void setResetWhenLinkage(boolean resetWhenLinkage) {
        this.resetWhenLinkage = resetWhenLinkage;
    }

    public Integer getSelectedYear() {
        return selectedYear;
    }

    public Integer getSelectedMonth() {
        return selectedMonth;
    }

    public Integer getSelectedDay() {
        return selectedDay;
    }

    private void changeYear() {
        final int min = Math.min(startValue.getYear(), endValue.getYear());
        final int max = Math.max(startValue.getYear(), endValue.getYear());
        if (selectedYear == null) {
            selectedYear = min;
        } else {
            selectedYear = Math.max(selectedYear, min);
            selectedYear = Math.min(selectedYear, max);
        }
        wheelYear.setRange(min, max, 1);
        wheelYear.setDefaultValue(selectedYear);
        changeMonth(selectedYear);
    }

    private void changeMonth(int year) {
        final int min, max;
        //开始年份和结束年份相同（即只有一个年份，这种情况建议使用月日模式）
        if (startValue.getYear() == endValue.getYear()) {
            min = Math.min(startValue.getMonth(), endValue.getMonth());
            max = Math.max(startValue.getMonth(), endValue.getMonth());
        }
        //当前所选年份和开始年份相同
        else if (year == startValue.getYear()) {
            min = startValue.getMonth();
            max = 12;
        }
        //当前所选年份和结束年份相同
        else if (year == endValue.getYear()) {
            min = 1;
            max = endValue.getMonth();
        }
        //当前所选年份在开始年份和结束年份之间
        else {
            min = 1;
            max = 12;
        }
        if (selectedMonth == null) {
            selectedMonth = min;
        } else {
            selectedMonth = Math.max(selectedMonth, min);
            selectedMonth = Math.min(selectedMonth, max);
        }
        wheelMonth.setRange(min, max, 1);
        wheelMonth.setDefaultValue(selectedMonth);
        changeDay(year, selectedMonth);
    }

    private void changeDay(int year, int month) {
        final int min, max;
        //开始年月及结束年月相同情况
        if (year == startValue.getYear() && month == startValue.getMonth()
                && year == endValue.getYear() && month == endValue.getMonth()) {
            min = startValue.getDay();
            max = endValue.getDay();
        }
        //开始年月相同情况
        else if (year == startValue.getYear() && month == startValue.getMonth()) {
            min = startValue.getDay();
            max = getTotalDaysInMonth(year, month);
        }
        //结束年月相同情况
        else if (year == endValue.getYear() && month == endValue.getMonth()) {
            min = 1;
            max = endValue.getDay();
        } else {
            min = 1;
            max = getTotalDaysInMonth(year, month);
        }
        if (selectedDay == null) {
            selectedDay = min;
        } else {
            selectedDay = Math.max(selectedDay, min);
            selectedDay = Math.min(selectedDay, max);
        }
        wheelDay.setRange(min, max, 1);
        wheelDay.setDefaultValue(selectedDay);
        dateSelectedCallback();
    }

    /**
     * 根据年份及月份获取每月的天数，类似于{@link java.util.Calendar#getActualMaximum(int)}
     */
    private int getTotalDaysInMonth(int year, int month) {
        switch (month) {
            case 1:
            case 3:
            case 5:
            case 7:
            case 8:
            case 10:
            case 12:
                // 大月月份为31天
                return 31;
            case 4:
            case 6:
            case 9:
            case 11:
                // 小月月份为30天
                return 30;
            case 2:
                // 二月需要判断是否闰年
                if (year <= 0) {
                    return 29;
                }
                // 是否闰年：能被4整除但不能被100整除；能被400整除；
                boolean isLeap = (year % 4 == 0 && year % 100 != 0) || year % 400 == 0;
                if (isLeap) {
                    return 29;
                } else {
                    return 28;
                }
            default:
                return 30;
        }
    }
}
