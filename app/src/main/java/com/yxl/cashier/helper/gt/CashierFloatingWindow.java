package com.yxl.cashier.helper.gt;

import android.annotation.SuppressLint;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Point;
import android.graphics.Rect;
import android.hardware.display.DisplayManager;
import android.hardware.display.VirtualDisplay;
import android.media.Image;
import android.media.ImageReader;
import android.media.projection.MediaProjection;
import android.media.projection.MediaProjectionManager;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.google.android.gms.tasks.OnFailureListener;
import com.google.android.gms.tasks.OnSuccessListener;
import com.google.mlkit.vision.common.InputImage;
import com.google.mlkit.vision.text.Text;
import com.google.mlkit.vision.text.TextRecognition;
import com.google.mlkit.vision.text.TextRecognizer;
import com.google.mlkit.vision.text.chinese.ChineseTextRecognizerOptions;
import com.googlecode.tesseract.android.TessBaseAPI;
import com.gsls.gt.GT;
import com.yxl.cashier.helper.LauncherActivity;
import com.yxl.cashier.helper.MyApplication;
import com.yxl.cashier.helper.R;
import com.yxl.cashier.helper.utils.BitmapUtils;
import com.yxl.cashier.helper.utils.SDUtils;
import com.yxl.commonlibrary.Constants;
import com.yxl.commonlibrary.base.BaseApplication;
import com.yxl.commonlibrary.utils.DFUtils;
import com.yxl.commonlibrary.utils.FileUtils;
import com.yxl.commonlibrary.utils.SpUtils;
import com.yxl.commonlibrary.utils.StringUtils;

import java.io.File;
import java.nio.ByteBuffer;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * Describe:悬浮窗（收银）
 * Created by jingang on 2023/11/30
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n", "ClickableViewAccessibility"})
@GT.Annotations.GT_AnnotationFloatingWindow(R.layout.floating_cashier)
public class CashierFloatingWindow extends GT.GT_FloatingWindow.AnnotationFloatingWindow {
    private final String tag = "CashierFloatingWindow";
    @GT.Annotations.GT_View(R.id.etTest)
    EditText etTest;
    @GT.Annotations.GT_View(R.id.tvUnit)
    TextView tvUnit;
    @GT.Annotations.GT_View(R.id.tvMoney)
    TextView tvMoney;
    @GT.Annotations.GT_View(R.id.ivImg)
    ImageView ivImg;
    @GT.Annotations.GT_View(R.id.ivFocus)
    ImageView ivFocus;//是否获取焦点

    //截图位置
    private int left, top, right, bottom, width, height;
    private boolean isMoved,//触摸是否移动
            isFocus;//是否获取焦点

    private Executor executor;
    private long startTime, endTime;//耗时记录
    private int mRecognitionCount = 0; // 识别相同文本次数
    private String mLastRecognitionStr = ""; // 上次识别的文本

    @Override
    protected void initView(View view) {
        super.initView(view);
//        setDrag(true);//设置可拖动
        //监听扫码枪扫码
        etTest.requestFocus();
        etTest.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == EditorInfo.IME_ACTION_DONE ||
                    (event != null && event.getAction() == KeyEvent.ACTION_DOWN && event.getKeyCode() == KeyEvent.KEYCODE_ENTER)) {
                // 当点击软键盘上的“完成”按钮或按下硬件/虚拟Enter键时执行以下操作
                Log.e("111111", "扫码枪 = " + etTest.getText().toString());

                double money;
                String barcode = etTest.getText().toString();
                etTest.setText("");
                if (!TextUtils.isEmpty(barcode)) {
                    MyApplication.type = 0;
                    MyApplication.barCode = barcode;
                    try {
                        money = Double.parseDouble(tvMoney.getText().toString());
                        if (money > 0) {
                            MyApplication.cashierMoney = DFUtils.getNum4(money);
                        } else {
                            MyApplication.cashierMoney = "";
                        }
                    } catch (Exception e) {
                        MyApplication.cashierMoney = "";
                    }

                    GT.startFloatingWindow(CashierFloatingWindow.this, MainFloatingWindow.class);
                    finish();
                }
                return true; // 表示已处理该事件
            }
            return false;
        });
        //获取焦点不弹出软键盘
        etTest.setOnFocusChangeListener((v, hasFocus) -> {
            if (hasFocus) {
                InputMethodManager imm = (InputMethodManager) view.getContext().getSystemService(INPUT_METHOD_SERVICE);
                if (imm != null) {
                    imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
                }
            }
        });
    }

    @Override
    public void onCreate() {
        super.onCreate();
//        setGetFocus(true);//是否获取焦点，获取焦点后才能进行弹出软键盘
        isFocus = BaseApplication.getInstance().isFocus();
        setGetFocus(isFocus);
        ivFocus.setSelected(isFocus);

        executor = Executors.newSingleThreadExecutor(); // 创建一个单线程执行器
        left = (int) SpUtils.getInstance().get(Constants.LEFT, 0);
        top = (int) SpUtils.getInstance().get(Constants.TOP, 0);
        right = (int) SpUtils.getInstance().get(Constants.RIGHT, 0);
        bottom = (int) SpUtils.getInstance().get(Constants.BOTTOM, 0);
        width = (int) SpUtils.getInstance().get(Constants.CROP_WIDTH, 200);
        height = (int) SpUtils.getInstance().get(Constants.CROP_HEIGHT, 200);
        if (MyApplication.x0 == 0) {
            if (getWidth() > 300) {
                setXY(getWidth() - 300, 100);
            } else {
                setXY(0, 100);
            }
        } else {
            setXY(MyApplication.x0, MyApplication.y0);
        }

        getView().setOnTouchListener(new FloatingOnTouchListener());
    }

    @GT.Annotations.GT_Click({R.id.ivFocus})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.ivFocus:
                //是否获取焦点
                isFocus = !isFocus;
                setGetFocus(isFocus);
                ivFocus.setSelected(isFocus);
                if (isFocus) {
                    SpUtils.getInstance().put(Constants.isFocus, Constants.isFocus);
                } else {
                    SpUtils.getInstance().put(Constants.isFocus, "");
                }
                break;
        }
    }

    /**
     * 拖动事件
     */
    public class FloatingOnTouchListener extends GT.GT_FloatingWindow.BaseFloatingWindow.FloatingOnTouchListener {
        private int x;
        private int y;
        private WindowManager.LayoutParams layoutParams = getLayoutParams();
        private WindowManager windowManager = getWindowManager();

        @Override
        public boolean onTouch(View view, MotionEvent event) {
            mRecognitionCount = 0;
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    isMoved = false;
                    x = (int) event.getRawX();
                    y = (int) event.getRawY();
                    break;
                case MotionEvent.ACTION_MOVE:
                    isMoved = true;
                    int nowX = (int) event.getRawX();
                    int nowY = (int) event.getRawY();
                    int movedX = nowX - x;
                    int movedY = nowY - y;
                    x = nowX;
                    y = nowY;
                    layoutParams.x = layoutParams.x + movedX;
                    layoutParams.y = layoutParams.y + movedY;
                    windowManager.updateViewLayout(view, layoutParams);
                    break;
                case MotionEvent.ACTION_UP:
                    MyApplication.x0 = layoutParams.x;
                    MyApplication.y0 = layoutParams.y;
                    Log.e(tag, "isMoved = " + isMoved);
                    if (!isMoved) {
                        //触摸没有移动，所以触发点击事件
                        if (TextUtils.isEmpty(getToken())) {
                            GT.startFloatingWindow(GT.getActivity(), LoginFloatingWindow.class);
                            finish();
                        } else {
                            if (left == 0) {
                                GT.startFloatingWindow(CashierFloatingWindow.this, CropFloatingWindow.class);
                                finish();
                            } else {
                                double money;
                                try {
                                    money = Double.parseDouble(tvMoney.getText().toString());
                                    if (money > 0) {
                                        MyApplication.cashierMoney = DFUtils.getNum4(money);
                                    } else {
                                        MyApplication.cashierMoney = "";
                                    }
                                    MyApplication.type = 0;
                                } catch (Exception e) {
                                    MyApplication.cashierMoney = "";
                                }
                                GT.startFloatingWindow(CashierFloatingWindow.this, MainFloatingWindow.class);
                                finish();
                            }
                        }
                    }
                    break;
                default:
                    break;
            }
            return true;
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        // 当服务销毁时，取消前台通知
        stopForeground(true);
        tearDownMediaProjection();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        // 将Service设置为前台服务
        createNotificationChannel();
        // 返回START_STICKY保证服务在被系统终止后能重启
        return START_STICKY;
    }

    /**
     * 创建前台通知
     */
    private void createNotificationChannel() {
        startTime = System.currentTimeMillis();
        //Android8.0
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationManager notificationManager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
            NotificationChannel channel = new NotificationChannel(String.valueOf(Constants.notification_id),
                    Constants.notification_name,
                    NotificationManager.IMPORTANCE_MIN);
            channel.setSound(null, null); // 关闭声音
            channel.enableVibration(false); // 禁止震动
            notificationManager.createNotificationChannel(channel);
        }

        Notification.Builder builder = new Notification.Builder(this); //获取一个Notification构造器
        Intent nfIntent = new Intent(this, LauncherActivity.class); //点击后跳转的界面，可以设置跳转数据

        //普通notification适配
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            builder.setChannelId(String.valueOf(Constants.notification_id));
        }

        builder.setContentIntent(PendingIntent.getActivity(this, 0, nfIntent, 0)) // 设置PendingIntent
                .setLargeIcon(BitmapFactory.decodeResource(this.getResources(), R.mipmap.ic_launcher)) // 设置下拉列表中的图标(大图标)
                .setSmallIcon(R.mipmap.ic_launcher) // 设置状态栏内的小图标
                .setContentText("is running......") // 设置上下文内容
                .setVibrate(null)
                .setSound(null)
                .setLights(0, 0, 0)
                .setWhen(System.currentTimeMillis()); // 设置该通知发生的时间

        Notification notification = builder.build(); // 获取构建好的Notification
        startForeground(Constants.notification_id, notification);

        setUI();
    }

    /**
     * 更新UI
     */
    private void setUI() {
        tvUnit.setVisibility(View.GONE);
        if (TextUtils.isEmpty(getToken())) {
            tvMoney.setText("请登录");
        } else {
            if (left == 0) {
                tvMoney.setText("设置识别区域");
            } else {
                tvMoney.setText("正在识别");
                executor.execute(() -> {
                    // 在这里执行耗时操作
                    createVirtualEnvironment();
                });
            }
        }
    }

    /**********************************截屏start*********************************/

    public static MediaProjectionManager mMediaProjectionManager = null;
    private int windowWidth = 0,
            windowHeight = 0,
            mScreenDensity = 0;
    private DisplayMetrics metrics = null;
    private ImageReader mImageReader = null;

    private MediaProjection mMediaProjection = null;
    public static Intent mResultData = null;
    public static int mResultCode = 0;
    private VirtualDisplay mVirtualDisplay = null;
    private Bitmap mBitmap,//截屏
            croppedBitmap;//裁剪

    /**
     * 创建截屏
     */
    @SuppressLint("WrongConstant")
    private void createVirtualEnvironment() {
        mMediaProjectionManager = ((BaseApplication) getApplication()).getMediaProjectionManager();
        if (mMediaProjectionManager == null) {
            mMediaProjectionManager = (MediaProjectionManager) getApplication().getSystemService(Context.MEDIA_PROJECTION_SERVICE);
        }
        Log.e(tag, "mMediaProjectionManager0 = " + mMediaProjectionManager);
        windowWidth = getWindowManager().getDefaultDisplay().getWidth();
        windowHeight = getWindowManager().getDefaultDisplay().getHeight();
        metrics = new DisplayMetrics();
        getWindowManager().getDefaultDisplay().getMetrics(metrics);
        mScreenDensity = metrics.densityDpi;
        mImageReader = ImageReader.newInstance(windowWidth, windowHeight, 0x1, 2); //ImageFormat.RGB_565
        Looper.prepare();
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            try {
                if (mMediaProjection == null) {
                    mResultData = ((BaseApplication) getApplication()).getIntent();
                    mResultCode = ((BaseApplication) getApplication()).getResult();
                    Log.e(tag, "mMediaProjectionManager = " + mMediaProjectionManager);
                    if (mMediaProjectionManager != null) {
                        mMediaProjection = mMediaProjectionManager.getMediaProjection(mResultCode, mResultData);
                    }
                }
                Log.e(tag, "mMediaProjection = " + mMediaProjection);
                if (mMediaProjection != null) {
                    mVirtualDisplay = mMediaProjection.createVirtualDisplay("screen-mirror",
                            windowWidth, windowHeight, mScreenDensity, DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR,
                            mImageReader.getSurface(), null, null);
                }
                new Handler().postDelayed(this::startCapture, 500);
            } catch (SecurityException e) {
                Log.e(tag, "e = " + e.getMessage());
            }
        }, 500);
        Looper.loop();
    }

    /**
     * 获取录屏结果
     */
    private void startCapture() {
        Log.e(tag, "mImageReader = " + mImageReader);
        Image image = mImageReader.acquireLatestImage();
        Log.e(tag, "image = " + image);
        if (image != null) {
            int width = image.getWidth();
            int height = image.getHeight();
            final Image.Plane[] planes = image.getPlanes();
            final ByteBuffer buffer = planes[0].getBuffer();
            int pixelStride = planes[0].getPixelStride();
            int rowStride = planes[0].getRowStride();
            int rowPadding = rowStride - pixelStride * width;
            mBitmap = Bitmap.createBitmap(width + rowPadding / pixelStride, height, Bitmap.Config.ARGB_8888);
            mBitmap.copyPixelsFromBuffer(buffer);
            mBitmap = Bitmap.createBitmap(mBitmap, 0, 0, width, height);
            image.close();
            endTime = System.currentTimeMillis();//录屏结束耗时
            Log.e(tag, "录屏耗时：" + (endTime - startTime) + "毫秒");
            startTime = System.currentTimeMillis();//截图开始
            Message message = new Message();
            message.what = 0;
            myHandler.sendMessage(message);
        }
    }

    /**
     * TessBaseAPI初始化用到的第一个参数，是个目录。
     */
    private static final String DATAPATH = FileUtils.getPath(GT.getActivity()) + File.separator;
    /**
     * 在DATAPATH中新建这个目录，TessBaseAPI初始化要求必须有这个目录。
     */
    private final String tessdata = DATAPATH + File.separator + "tessdata";
    /**
     * TessBaseAPI初始化测第二个参数，就是识别库的名字不要后缀名。
     */
    private String DEFAULT_LANGUAGE = "eng";
    /**
     * assets中的文件名
     */
    private String DEFAULT_LANGUAGE_NAME = DEFAULT_LANGUAGE + ".traineddata";
    /**
     * 保存到SD卡中的完整文件名
     */
    private String LANGUAGE_PATH = tessdata + File.separator + DEFAULT_LANGUAGE_NAME;

    private TessBaseAPI tessBaseAPI;

    /**
     * 识别图像
     * 确保输入给OCR的图片是清晰且聚焦在数字上的，对图像进行裁剪、灰度化、二值化等预处理操作以提高识别准确率。
     */
    private void recognition() {
        new Thread(() -> {
            String text = "";
            if (!checkTraineddataExists()) {
                text += LANGUAGE_PATH + "不存在，开始复制\r\n";
                Log.i(tag, "run: " + LANGUAGE_PATH + "不存在，开始复制\r\n");
                SDUtils.assets2SD(getApplicationContext(), LANGUAGE_PATH, DEFAULT_LANGUAGE_NAME);
            }
            text += LANGUAGE_PATH + "已经存在，开始识别\r\n";
            long startTime = System.currentTimeMillis();
            // 初始化OCR引擎
            tessBaseAPI = new TessBaseAPI();
//            tessBaseAPI.init(DATAPATH, DEFAULT_LANGUAGE, TessBaseAPI.OEM_TESSERACT_ONLY); // 可以考虑使用专门为数字优化的版本
//            tessBaseAPI.setVariable(TessBaseAPI.VAR_CHAR_WHITELIST, "0123456789."); // 设置白名单

            tessBaseAPI.init(DATAPATH, DEFAULT_LANGUAGE); // 可以考虑使用专门为数字优化的版本
            tessBaseAPI.setPageSegMode(TessBaseAPI.PageSegMode.PSM_SINGLE_LINE); // 对于单行数字识别，可以使用PSM_SINGLE_LINE模式
            tessBaseAPI.setVariable(TessBaseAPI.VAR_CHAR_WHITELIST, "0123456789."); // 设置白名单

            tessBaseAPI.setImage(croppedBitmap);
            text = text + "识别结果：" + tessBaseAPI.getUTF8Text();
            long finishTime = System.currentTimeMillis();
            text = text + "\r\n" + " 耗时" + (finishTime - startTime) + "毫秒";
            Log.e(tag, "text = " + text);
            Message message = new Message();
            message.what = 1;
            message.obj = tessBaseAPI.getUTF8Text();
            myHandler.sendMessage(message);
            tessBaseAPI.end();
        }).start();
    }

    @SuppressLint("HandlerLeak")
    Handler myHandler = new Handler() {
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case 0:
                    //截屏-图片裁剪
                    if (mBitmap != null) {
                        try {
                            croppedBitmap = BitmapUtils.getCropImg(mBitmap, width, height, left, top);
                            endTime = System.currentTimeMillis();//截图结束耗时
                            Log.e(tag, "截图耗时：" + (endTime - startTime) + "毫秒");
                            if (croppedBitmap != null) {
//                                ivImg.setImageBitmap(croppedBitmap);
//                                recognition();
                                TextRecognizer recognizer =
                                        TextRecognition.getClient(new ChineseTextRecognizerOptions.Builder().build());
                                InputImage image = InputImage.fromBitmap(croppedBitmap, 0);
                                recognizer.process(image)
                                        .addOnSuccessListener(new OnSuccessListener<Text>() {
                                            @Override
                                            public void onSuccess(Text result) {
                                                String resultText = result.getText();
                                                for (Text.TextBlock block : result.getTextBlocks()) {
                                                    String blockText = block.getText();
                                                    Point[] blockCornerPoints = block.getCornerPoints();
                                                    Rect blockFrame = block.getBoundingBox();
                                                    for (Text.Line line : block.getLines()) {
                                                        String lineText = line.getText();
                                                        Point[] lineCornerPoints = line.getCornerPoints();
                                                        Rect lineFrame = line.getBoundingBox();
                                                        for (Text.Element element : line.getElements()) {
                                                            String elementText = element.getText();
                                                            Point[] elementCornerPoints = element.getCornerPoints();
                                                            Rect elementFrame = element.getBoundingBox();
                                                            for (Text.Symbol symbol : element.getSymbols()) {
                                                                String symbolText = symbol.getText();
                                                                Point[] symbolCornerPoints = symbol.getCornerPoints();
                                                                Rect symbolFrame = symbol.getBoundingBox();
                                                            }
                                                        }
                                                    }
                                                }
                                                Message message = new Message();
                                                message.what = 1;
                                                message.obj = resultText;
                                                myHandler.sendMessage(message);
                                                if (mLastRecognitionStr.equals(resultText)) {
                                                    mRecognitionCount += 1;
                                                    if (mRecognitionCount > 20) {
                                                        mRecognitionCount = 20;
                                                    }
                                                } else {
                                                    mRecognitionCount = 0;
                                                }
                                                mLastRecognitionStr = resultText;
                                                Log.e(tag, "识别结果" + resultText);


                                            }
                                        })
                                        .addOnFailureListener(
                                                new OnFailureListener() {
                                                    @Override
                                                    public void onFailure(@NonNull Exception e) {

                                                    }
                                                });
                            } else {
                                startCapture();
                            }
                        } catch (Exception e) {
                            left = 0;
                            tvMoney.setText("设置识别区域");
                        }
                    } else {
                        startCapture();
                    }
                    break;
                case 1:
                    //OCR识别结果输出
                    String str = StringUtils.getHandledOcrMoney(String.valueOf(msg.obj));
                    tvUnit.setVisibility(View.VISIBLE);
                    if (TextUtils.isEmpty(str)) {
                        tvMoney.setText("0.00");
                    } else {
                        tvMoney.setText(str);
                    }
                    new Handler().postDelayed(() -> {
                        startCapture();
                    }, mRecognitionCount > 10 ? mRecognitionCount * 100L : 100);
                    break;
            }
            super.handleMessage(msg);
        }
    };

    public boolean checkTraineddataExists() {
        File file = new File(LANGUAGE_PATH);
        return file.exists();
    }


    private void tearDownMediaProjection() {
        if (mMediaProjection != null) {
            mMediaProjection.stop();
            mMediaProjection = null;
        }
        stopVirtual();
    }

    private void stopVirtual() {
        if (mVirtualDisplay == null) {
            return;
        }
        mVirtualDisplay.release();
        mVirtualDisplay = null;
    }
    /**********************************截屏end*********************************/

    /**
     * 登录token
     *
     * @return
     */
    private String getToken() {
        return BaseApplication.getInstance().getToken();
    }

}
