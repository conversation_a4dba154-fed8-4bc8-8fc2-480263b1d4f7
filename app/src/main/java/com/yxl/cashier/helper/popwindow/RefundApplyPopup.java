package com.yxl.cashier.helper.popwindow;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.drawable.AnimationDrawable;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.yxl.cashier.helper.R;
import com.yxl.cashier.helper.utils.ViewUtils;
import com.yxl.cashier.helper.view.NumberKeyBoardView;

/**
 * Describe:popup（退款审批）
 * Created by jingang on 2023/5/30
 */
@SuppressLint("NonConstantResourceId")
public class RefundApplyPopup extends PopupWindow {
    private static Context mContext;
    private LinearLayout linToast;
    private ImageView ivToast, ivClose, ivCursor;
    private TextView tvToast, tvPwd, tvHint;
    private NumberKeyBoardView keyBoardView;

    public RefundApplyPopup(Context context) {
        super(context);
        View view = View.inflate(context, R.layout.popup_refund_apply, null);
        setContentView(view);
        linToast = view.findViewById(R.id.linToast);
        ivToast = view.findViewById(R.id.ivToast);
        tvToast = view.findViewById(R.id.tvToast);
        ivClose = view.findViewById(R.id.ivPopupClose);
        tvPwd = view.findViewById(R.id.tvPopupPwd);
        tvHint = view.findViewById(R.id.tvPopupHint);
        ivCursor = view.findViewById(R.id.ivCursor);
        keyBoardView = view.findViewById(R.id.numberKeyBoardView);

        linToast.setAlpha(0f);
        ((AnimationDrawable) ivCursor.getDrawable()).start();
        ivClose.setOnClickListener(v -> dismiss());
        keyBoardView.setOnMValueChangedListener(new NumberKeyBoardView.OnMValueChangedListener() {
            @Override
            public void onChange(String var1) {
                tvPwd.setText(var1);
                if (TextUtils.isEmpty(var1)) {
                    tvHint.setVisibility(View.VISIBLE);
                } else {
                    tvHint.setVisibility(View.GONE);
                }
            }

            @Override
            public void onConfirm() {
                if (TextUtils.isEmpty(tvPwd.getText().toString().trim())) {
                    showToast(1, "请输入审批密码");
                    return;
                }
                if (tvPwd.getText().toString().trim().length() < 6) {
                    showToast(1, "请输入正确的审批密码（6位）");
                    return;
                }
                if (listener != null) {
                    listener.onRefundApply(tvPwd.getText().toString().trim());
                    dismiss();
                }
            }
        });
    }

    public static void showDialog(Context context, int width, int height, View viewShow, MyListener listener) {
        RefundApplyPopup.mContext = context;

        RefundApplyPopup popupWindow = new RefundApplyPopup(context);
        setListener(listener);
        popupWindow.setWidth(width + 2);
        popupWindow.setHeight(height + 4);
        //设置可以获取焦点
        popupWindow.setFocusable(false);
        //设置可以触摸弹出框以外的区域
        popupWindow.setOutsideTouchable(true);
        //放在具体控件下方
        popupWindow.showAsDropDown(viewShow);
    }

    /**
     * 吐司
     *
     * @param type 0.正确 1.错误
     * @param msg
     */
    private void showToast(int type, String msg) {
        if (type == 0) {
            linToast.setBackgroundResource(R.drawable.shape_green_5);
            ivToast.setImageResource(R.mipmap.ic_toast001);
        } else {
            linToast.setBackgroundResource(R.drawable.shape_red_5);
            ivToast.setImageResource(R.mipmap.ic_toast002);
        }
        tvToast.setText(msg);
        ViewUtils.fadeInAndFadeOut(linToast);
    }

    private static MyListener listener;

    public static void setListener(MyListener listener) {
        RefundApplyPopup.listener = listener;
    }

    public interface MyListener {
        void onRefundApply(String pwd);
    }
}
