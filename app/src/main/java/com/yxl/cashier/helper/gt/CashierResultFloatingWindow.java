package com.yxl.cashier.helper.gt;

import android.annotation.SuppressLint;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Intent;
import android.graphics.BitmapFactory;
import android.os.Build;
import android.os.CountDownTimer;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.google.gson.Gson;
import com.gsls.gt.GT;
import com.yxl.cashier.helper.BuildConfig;
import com.yxl.cashier.helper.LauncherActivity;
import com.yxl.cashier.helper.MyApplication;
import com.yxl.cashier.helper.R;
import com.yxl.cashier.helper.ZURL;
import com.yxl.cashier.helper.bean.PayData;
import com.yxl.cashier.helper.popwindow.IPopupWindow;
import com.yxl.cashier.helper.utils.SystemTTS;
import com.yxl.cashier.helper.utils.ViewUtils;
import com.yxl.commonlibrary.Constants;
import com.yxl.commonlibrary.base.BaseApplication;
import com.yxl.commonlibrary.base.BaseData;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DFUtils;
import com.yxl.commonlibrary.utils.DensityUtils;
import com.yxl.commonlibrary.utils.NetWorkUtils;
import com.yxl.commonlibrary.utils.SpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * Describe:收银结果
 * Created by jingang on 2024/1/23
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
@GT.Annotations.GT_AnnotationFloatingWindow(R.layout.floating_cashier_result)
public class CashierResultFloatingWindow extends GT.GT_FloatingWindow.AnnotationFloatingWindow {
    @GT.Annotations.GT_View(R.id.vTop)
    View mV;
    @GT.Annotations.GT_View(R.id.linToast)
    LinearLayout linToast;
    @GT.Annotations.GT_View(R.id.ivToast)
    ImageView ivToast;
    @GT.Annotations.GT_View(R.id.tvToast)
    TextView tvToast;
    @GT.Annotations.GT_View(R.id.tvTitle)
    TextView tvTitle;//收银状态
    @GT.Annotations.GT_View(R.id.ivStatus)
    ImageView ivStatus;
    @GT.Annotations.GT_View(R.id.tvStatus)
    TextView tvStatus;
    @GT.Annotations.GT_View(R.id.tvMoney)
    TextView tvMoney;//实收金额
    @GT.Annotations.GT_View(R.id.tvDisCount)
    TextView tvDiscount;//优惠金额
    @GT.Annotations.GT_View(R.id.tvTips)
    TextView tvTips;//失败原因
    @GT.Annotations.GT_View(R.id.tvDownTimer)
    TextView tvDownTimer;//收银完成最小化倒计时
    @GT.Annotations.GT_View(R.id.tvCancel)
    TextView tvCancel;//取消收银
    @GT.Annotations.GT_View(R.id.tvClose)
    TextView tvClose;//关闭窗口
    @GT.Annotations.GT_View(R.id.tvBack)
    TextView tvBack;//返回收银台

    private PayData data;
    private int status;//0.支付中 1.支付成功 2.支付失败


    @Override
    protected void initView(View view) {
        super.initView(view);
        setDrag(true);//设置可拖动
        linToast.setAlpha(0f);
        getPayResult();
    }

    @Override
    public void onCreate() {
        super.onCreate();
        setGetFocus(true);//是否获取焦点，获取焦点后才能进行弹出软键盘
        updateView(DensityUtils.getFloatingWindowWidth(getWidth()), DensityUtils.getFloatingWindowHeight(getHeight()));
        //绝对位置（屏幕中间）
        setXY(getWidth() / 2 - DensityUtils.getFloatingWindowWidth(getWidth()) / 2,
                getHeight() / 2 - DensityUtils.getFloatingWindowHeight(getHeight()) / 2);
    }

    @GT.Annotations.GT_Click({R.id.ivBack, R.id.ivClose, R.id.tvCancel, R.id.tvClose, R.id.tvBack})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                //返回
            case R.id.tvBack:
                //返回收银台
                if (status != 0) {
                    GT.startFloatingWindow(GT.getActivity(), MainFloatingWindow.class);
                    finish();
                }
                break;
            case R.id.ivClose:
            case R.id.tvClose:
                //关闭窗口
                if (status != 0) {
                    GT.startFloatingWindow(this, CashierFloatingWindow.class);
                    finish();
                }
                break;
            case R.id.tvCancel:
                //取消付款
                if (status == 0) {
                    IPopupWindow.showDialog(this,
                            DensityUtils.getFloatingWindowWidth(getWidth()),
                            DensityUtils.getFloatingWindowHeight(getHeight()),
                            mV, "确认取消付款？", "确认", () -> {
                                postCancelPay();
                            });
                }
                break;
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        cancelTimer();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        // 将Service设置为前台服务
        createNotificationChannel();
        return START_STICKY;
    }

    /**
     * 创建前台通知
     */
    private void createNotificationChannel() {
        //Android8.0
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationManager notificationManager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
            NotificationChannel channel = new NotificationChannel(String.valueOf(Constants.notification_id),
                    Constants.notification_name,
                    NotificationManager.IMPORTANCE_MIN);
            channel.setSound(null, null); // 关闭声音
            channel.enableVibration(false); // 禁止震动
            notificationManager.createNotificationChannel(channel);
        }

        Notification.Builder builder = new Notification.Builder(this); //获取一个Notification构造器
        Intent nfIntent = new Intent(this, LauncherActivity.class); //点击后跳转的界面，可以设置跳转数据

        //普通notification适配
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            builder.setChannelId(String.valueOf(Constants.notification_id));
        }

        builder.setContentIntent(PendingIntent.getActivity(this, 0, nfIntent, 0)) // 设置PendingIntent
                .setLargeIcon(BitmapFactory.decodeResource(this.getResources(), R.mipmap.ic_launcher)) // 设置下拉列表中的图标(大图标)
                .setSmallIcon(R.mipmap.ic_launcher) // 设置状态栏内的小图标
                .setContentText("is running......") // 设置上下文内容
                .setVibrate(null)
                .setSound(null)
                .setLights(0, 0, 0)
                .setWhen(System.currentTimeMillis()); // 设置该通知发生的时间

        Notification notification = builder.build(); // 获取构建好的Notification
        startForeground(Constants.notification_id, notification);
    }

    /**
     * 更新UI
     */
    private void setUI() {
        if (data == null) {
            setUIPayFail("未知");
            return;
        }
        if (TextUtils.isEmpty(data.getPayStatus())) {
            setUIPayFail("未知");
            return;
        }
        //INIT-未支付,DOING-支付中，SUCCESS-支付成功，FAIL-支付失败，CLOSE-订单已关闭，CANCEL-订单已取消
        switch (data.getPayStatus()) {
            case "SUCCESS":
                //判断是否结算完成最小化
                setUIPaySuccess();
                break;
            case "DOING":
                //收银中-每2s轮巡一次接口
                setUIPaying();
                new Handler(Looper.getMainLooper()).postDelayed(this::getPayResult, 2000);
                break;
            case "INIT":
                setUIPayFail("未支付");
                break;
            case "FAIL":
                setUIPayFail("支付失败");
                break;
            case "CLOSE":
                setUIPayFail("订单已关闭");
                break;
            case "CANCEL":
                setUIPayFail("订单已取消");
                break;
            default:
                setUIPayFail("未知");
                break;
        }

    }

    /**
     * 收银成功
     */
    private void setUIPaySuccess() {
        status = 1;
        if (MyApplication.systemTTS == null) {
            MyApplication.systemTTS = SystemTTS.getInstance(MyApplication.getInstance());
        }
        MyApplication.systemTTS.playText("收银成功" + data.getActualAmount() + "元");
        tvTitle.setText("收银成功");
        ivStatus.setImageResource(R.mipmap.ic_status001);
        tvStatus.setText("收银成功");
        tvStatus.setTextColor(getResources().getColor(R.color.green));
        tvMoney.setVisibility(View.VISIBLE);
        tvMoney.setText("实收金额：￥" + DFUtils.getNum2(data.getActualAmount()));
        if (data.getDiscountAmount() > 0) {
            tvDiscount.setVisibility(View.VISIBLE);
            tvDiscount.setText("优惠金额：￥" + DFUtils.getNum2(data.getDiscountAmount()));
        } else {
            tvDiscount.setVisibility(View.GONE);
        }
        tvTips.setVisibility(View.GONE);
        if (BaseApplication.getInstance().isPayFinish()) {
            tvDownTimer.setVisibility(View.VISIBLE);
            time();
        } else {
            tvDownTimer.setVisibility(View.GONE);
        }
        tvCancel.setVisibility(View.GONE);
        tvClose.setVisibility(View.VISIBLE);
        tvBack.setVisibility(View.VISIBLE);
    }

    /**
     * 收银失败
     *
     * @param tips
     */
    private void setUIPayFail(String tips) {
        status = 2;
        if (MyApplication.systemTTS == null) {
            MyApplication.systemTTS = SystemTTS.getInstance(MyApplication.getInstance());
        }
        MyApplication.systemTTS.playText("收银失败" + tips);
        tvTitle.setText("收银失败");
        ivStatus.setImageResource(R.mipmap.ic_status002);
        tvStatus.setText("收银失败");
        tvStatus.setTextColor(getResources().getColor(R.color.red));
        tvMoney.setVisibility(View.GONE);
        tvDiscount.setVisibility(View.GONE);
        tvTips.setVisibility(View.VISIBLE);
        tvTips.setText("失败原因：\n" + tips);
        tvDownTimer.setVisibility(View.GONE);
        tvCancel.setVisibility(View.GONE);
        tvClose.setVisibility(View.VISIBLE);
        tvBack.setVisibility(View.VISIBLE);
    }

    /**
     * 收银中
     */
    private void setUIPaying() {
        status = 0;
        tvTitle.setText("收银中...");
        Glide.with(GT.getActivity())
                .asGif()
                .load(R.drawable.cashiering)
                .into(ivStatus);
        tvStatus.setText("收银中");
        tvStatus.setTextColor(getResources().getColor(R.color.color_999));
        tvMoney.setVisibility(View.VISIBLE);
        tvMoney.setText("应收金额：￥" + DFUtils.getNum2(data.getTotalAmount()));
        tvDiscount.setVisibility(View.GONE);
        tvTips.setVisibility(View.VISIBLE);
        tvTips.setVisibility(View.VISIBLE);
        tvTips.setText("若由于网络异常等原因导致支付时间较长，\n您可以点击“关闭窗口”按钮，后续可在订单中查看支付结果");
        tvDownTimer.setVisibility(View.GONE);
        tvCancel.setVisibility(View.VISIBLE);
        tvClose.setVisibility(View.VISIBLE);
        tvBack.setVisibility(View.GONE);
    }


    /**************************倒计时start**************************/
    //倒计时
    private CountDownTimer countdown;

    /**
     * 倒计时（弹出退出登录弹窗）
     */
    public void time() {
        cancelTimer();
        if (countdown == null) {
            countdown = new CountDownTimer(Constants.finish_time * 1000L, 1000L) {
                @SuppressLint("SetTextI18n")
                @Override
                public void onTick(long millisUntilFinished) {
                    if (tvDownTimer != null) {
                        tvDownTimer.setText((millisUntilFinished / 1000) + "秒后关闭窗口");
                    }
                }

                @Override
                public void onFinish() {
                    //倒计时结束时触发
                    GT.startFloatingWindow(CashierResultFloatingWindow.this, CashierFloatingWindow.class);
                    finish();
                }
            };
            countdown.start();
        } else {
            countdown.start();
        }
    }

    /**
     * 计时器清除（没暂停）
     */
    public void cancelTimer() {
        if (countdown != null) {
            countdown.cancel();
        }
    }

    /**************************倒计时end**************************/

    /**
     * 支付结果查询
     */
    private void getPayResult() {
        if (!NetWorkUtils.isConnect(this)) {
            showToast(1, "无网络连接");
            return;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("saleListUnique", MyApplication.saleListUnique);
        RXHttpUtil.requestByGetAsResponse(this,
                ZURL.getPayResult(),
                map,
                PayData.class,
                new RequestListener<PayData>() {
                    @Override
                    public void success(PayData payData) {
                        data = payData;
                        setUI();
                    }

                    @Override
                    public void onError(String msg, String code) {
                        if (!TextUtils.isEmpty(code)) {
                            if (code.equals("401")) {
//                                showToast(1, msg);
//                                BaseApplication.getInstance().saveUserInfo("");
//                                GT.startFloatingWindow(GT.getActivity(), LoginFloatingWindow.class);
//                                finish();
                                postLogin(0);
                            } else {
                                setUIPayFail(msg);
                            }
                        }
                    }
                });
    }

    /**
     * 撤销订单（取消付款）
     */
    private void postCancelPay() {
        if (!NetWorkUtils.isConnect(this)) {
            showToast(1, "无网络连接");
            return;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("saleListUnique", MyApplication.saleListUnique);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getPayCancel(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        showToast(1, s);
                        GT.startFloatingWindow(GT.getActivity(), MainFloatingWindow.class);
                        finish();
                    }

                    @Override
                    public void onError(String msg, String code) {
                        if (!TextUtils.isEmpty(code)) {
                            if (code.equals("401")) {
//                                BaseApplication.getInstance().saveUserInfo("");
//                                GT.startFloatingWindow(GT.getActivity(), LoginFloatingWindow.class);
//                                finish();
                                postLogin(1);
                            } else {
                                showToast(1, msg);
                            }
                        }
                    }
                });
    }

    /**
     * 登录
     *
     * @param type 0.支付结果查询 1.取消付款
     */
    private void postLogin(int type) {
        if (!NetWorkUtils.isConnect(this)) {
            showToast(1, "无网络连接");
            return;
        }
        String account = BaseApplication.getInstance().getAccount();
        String pwd = BaseApplication.getInstance().getPwd();
        if (TextUtils.isEmpty(account)) {
            goToLogin();
            return;
        }
        if (account.length() < 11) {
            goToLogin();
            return;
        }
        if (TextUtils.isEmpty(pwd)) {
            goToLogin();
            return;
        }
        if (pwd.length() < 6) {
            goToLogin();
            return;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("phone", account);
        map.put("password", pwd);
        RXHttpUtil.requestByBodyPostAsOriginalResponse(this,
                ZURL.getLogin(),
                map,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        Log.e("111111", "登录 = " + s);
                        BaseData data = new Gson().fromJson(s, BaseData.class);
                        if (data == null) {
                            goToLogin();
                            return;
                        }
                        if (data.getCode() != Constants.SUCCESS_CODE) {
                            goToLogin();
                            return;
                        }
                        if (data.getData() == null) {
                            goToLogin();
                            return;
                        }
                        BaseApplication.getInstance().saveUserInfo(s);
                        if (type == 0) {
                            getPayResult();
                        } else {
                            postCancelPay();
                        }
                    }

                    @Override
                    public void onError(String msg, String code) {
                        Log.e("111111", "登录 s = " + msg + " code = " + code);
                        goToLogin();
                    }
                });
    }

    /**
     * 跳转到登录页
     */
    private void goToLogin() {
        BaseApplication.getInstance().saveUserInfo("");
        GT.startFloatingWindow(GT.getActivity(), LoginFloatingWindow.class);
        finish();
    }

    /**
     * 吐司
     *
     * @param type 0.正确 1.错误
     * @param msg
     */
    private void showToast(int type, String msg) {
        if (type == 0) {
            ivToast.setImageResource(R.mipmap.ic_toast001);
        } else {
            ivToast.setImageResource(R.mipmap.ic_toast002);
        }
        tvToast.setText(msg);
        ViewUtils.fadeInAndFadeOut(linToast);
    }

}
