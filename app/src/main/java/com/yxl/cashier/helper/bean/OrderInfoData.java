package com.yxl.cashier.helper.bean;

import java.io.Serializable;
import java.util.List;

/**
 * Describe:订单详情（实体类）
 * Created by jingang on 2024/1/17
 */
public class OrderInfoData implements Serializable {
    /**
     * saleListId : 116957
     * saleListUnique : 1704274271214786
     * saleListTotal : 0.10
     * discountAmount : 0.00
     * saleListActuallyReceived : 0.10
     * saleListState : 3
     * payTime : null
     * cusName : null
     * cusPhone : null
     * cusLevel : null
     * retTotalAmount : 0.08
     * returnList : [{"id":933,"retListDatetime":"2024-01-03 17:31:48","retListTotal":"0.08","retListState":"2","retListHandlestate":"3","retListTotalMoney":"0.08","staffName":"益农社服务员"}]
     */

    private int saleListId;
    private String saleListUnique;
    private String tradeNo;//支付单号
    //    private String saleListTotal;//订单金额
//    private String discountAmount;//优惠金额
//    private String saleListActuallyReceived;//实收金额
//    private String retTotalAmount;//退款总金额
    private double saleListTotal;//订单金额
    private double discountAmount;//优惠金额
    private double saleListActuallyReceived;//实收金额
    private double retTotalAmount;//退款总金额
    private int saleListState;//付款状态 2-未付款,3-已完成
    private String payStatus;//支付状态：CANCEL-已取消，DOING-未付款，SUCCESS-已支付，FAIL-已关闭
    private String payTime;//支付时间
    private String cusName;//会员名称
    private String cusPhone;//会员电话
    private String cusLevel;//会员等级
    private String saleListPayment;//支付方式：2-支付宝,3-微信,8-金圈支付,13-云闪付支付
    private String sourceType;//订单来源
    private String equipmentSourceType;//订单来源
    private String paymentMethod;//现金、小程序、金圈
    private int paymentMethodCode;//1.现金 8.小程序 13.金圈
    private List<DiscountListBean> discountList;//优惠列表
    private List<ReturnListBean> returnList;

    public int getSaleListId() {
        return saleListId;
    }

    public void setSaleListId(int saleListId) {
        this.saleListId = saleListId;
    }

    public String getSaleListUnique() {
        return saleListUnique;
    }

    public void setSaleListUnique(String saleListUnique) {
        this.saleListUnique = saleListUnique;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    //    public String getSaleListTotal() {
//        return saleListTotal;
//    }
//
//    public void setSaleListTotal(String saleListTotal) {
//        this.saleListTotal = saleListTotal;
//    }
//
//    public String getDiscountAmount() {
//        return discountAmount;
//    }
//
//    public void setDiscountAmount(String discountAmount) {
//        this.discountAmount = discountAmount;
//    }
//
//    public String getSaleListActuallyReceived() {
//        return saleListActuallyReceived;
//    }
//
//    public void setSaleListActuallyReceived(String saleListActuallyReceived) {
//        this.saleListActuallyReceived = saleListActuallyReceived;
//    }
//
//    public String getRetTotalAmount() {
//        return retTotalAmount;
//    }
//
//    public void setRetTotalAmount(String retTotalAmount) {
//        this.retTotalAmount = retTotalAmount;
//    }

    public double getSaleListTotal() {
        return saleListTotal;
    }

    public void setSaleListTotal(double saleListTotal) {
        this.saleListTotal = saleListTotal;
    }

    public double getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(double discountAmount) {
        this.discountAmount = discountAmount;
    }

    public double getSaleListActuallyReceived() {
        return saleListActuallyReceived;
    }

    public void setSaleListActuallyReceived(double saleListActuallyReceived) {
        this.saleListActuallyReceived = saleListActuallyReceived;
    }

    public double getRetTotalAmount() {
        return retTotalAmount;
    }

    public void setRetTotalAmount(double retTotalAmount) {
        this.retTotalAmount = retTotalAmount;
    }

    public int getSaleListState() {
        return saleListState;
    }

    public void setSaleListState(int saleListState) {
        this.saleListState = saleListState;
    }

    public String getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(String payStatus) {
        this.payStatus = payStatus;
    }

    public String getPayTime() {
        return payTime;
    }

    public void setPayTime(String payTime) {
        this.payTime = payTime;
    }

    public String getCusName() {
        return cusName;
    }

    public void setCusName(String cusName) {
        this.cusName = cusName;
    }

    public String getCusPhone() {
        return cusPhone;
    }

    public void setCusPhone(String cusPhone) {
        this.cusPhone = cusPhone;
    }

    public String getCusLevel() {
        return cusLevel;
    }

    public void setCusLevel(String cusLevel) {
        this.cusLevel = cusLevel;
    }

    public String getSaleListPayment() {
        return saleListPayment;
    }

    public void setSaleListPayment(String saleListPayment) {
        this.saleListPayment = saleListPayment;
    }

    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    public String getEquipmentSourceType() {
        return equipmentSourceType;
    }

    public void setEquipmentSourceType(String equipmentSourceType) {
        this.equipmentSourceType = equipmentSourceType;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public int getPaymentMethodCode() {
        return paymentMethodCode;
    }

    public void setPaymentMethodCode(int paymentMethodCode) {
        this.paymentMethodCode = paymentMethodCode;
    }

    public List<DiscountListBean> getDiscountList() {
        return discountList;
    }

    public void setDiscountList(List<DiscountListBean> discountList) {
        this.discountList = discountList;
    }

    public List<ReturnListBean> getReturnList() {
        return returnList;
    }

    public void setReturnList(List<ReturnListBean> returnList) {
        this.returnList = returnList;
    }

    public static class ReturnListBean {
        /**
         * id : 933
         * retListDatetime : 2024-01-03 17:31:48
         * retListTotal : 0.08
         * retListState : 2
         * retListHandlestate : 3
         * retListTotalMoney : 0.08
         * staffName : 益农社服务员
         */

        private int id;
        private String retListDatetime;//退款日期
        //        private String retListTotal;//退款总金额
//        private String retListTotalMoney;//实际退款金额
        private double retListTotal;//退款总金额
        private double retListTotalMoney;//实际退款金额
        private String retListState;//退款状态:1-未退款，2-已退款
        private String retListHandlestate;//退货申请受理状态：1未处理，2-已受理，3受理完毕;4、已驳回；
        private String staffName;//操作人姓名
        private String saleListUnique;//订单编号
        private String retListUnique;//退款订单编号

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getRetListDatetime() {
            return retListDatetime;
        }

        public void setRetListDatetime(String retListDatetime) {
            this.retListDatetime = retListDatetime;
        }

//        public String getRetListTotal() {
//            return retListTotal;
//        }
//
//        public void setRetListTotal(String retListTotal) {
//            this.retListTotal = retListTotal;
//        }
//
//        public String getRetListTotalMoney() {
//            return retListTotalMoney;
//        }
//
//        public void setRetListTotalMoney(String retListTotalMoney) {
//            this.retListTotalMoney = retListTotalMoney;
//        }


        public double getRetListTotal() {
            return retListTotal;
        }

        public void setRetListTotal(double retListTotal) {
            this.retListTotal = retListTotal;
        }

        public double getRetListTotalMoney() {
            return retListTotalMoney;
        }

        public void setRetListTotalMoney(double retListTotalMoney) {
            this.retListTotalMoney = retListTotalMoney;
        }

        public String getRetListState() {
            return retListState;
        }

        public void setRetListState(String retListState) {
            this.retListState = retListState;
        }

        public String getRetListHandlestate() {
            return retListHandlestate;
        }

        public void setRetListHandlestate(String retListHandlestate) {
            this.retListHandlestate = retListHandlestate;
        }

        public String getStaffName() {
            return staffName;
        }

        public void setStaffName(String staffName) {
            this.staffName = staffName;
        }

        public String getSaleListUnique() {
            return saleListUnique;
        }

        public void setSaleListUnique(String saleListUnique) {
            this.saleListUnique = saleListUnique;
        }

        public String getRetListUnique() {
            return retListUnique;
        }

        public void setRetListUnique(String retListUnique) {
            this.retListUnique = retListUnique;
        }
    }

    public static class DiscountListBean{
        private String name;
        private double money;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public double getMoney() {
            return money;
        }

        public void setMoney(double money) {
            this.money = money;
        }
    }
}
