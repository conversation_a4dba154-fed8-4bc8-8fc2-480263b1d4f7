package com.yxl.cashier.helper.gt;

import android.annotation.SuppressLint;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.drawable.BitmapDrawable;
import android.hardware.display.DisplayManager;
import android.hardware.display.VirtualDisplay;
import android.media.Image;
import android.media.ImageReader;
import android.media.projection.MediaProjection;
import android.media.projection.MediaProjectionManager;
import android.os.Build;
import android.os.Handler;
import android.os.Message;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.gsls.gt.GT;
import com.yxl.cashier.helper.LauncherActivity;
import com.yxl.cashier.helper.R;
import com.yxl.cashier.helper.view.CropImageView;
import com.yxl.commonlibrary.Constants;
import com.yxl.commonlibrary.base.BaseApplication;
import com.yxl.commonlibrary.utils.SpUtils;

import java.nio.ByteBuffer;

/**
 * Describe:悬浮窗（截图-设置识别区域）
 * Created by jingang on 2023/11/30
 */
@SuppressLint("NonConstantResourceId")
@GT.Annotations.GT_AnnotationFloatingWindow(R.layout.floating_crop)
public class CropFloatingWindow extends GT.GT_FloatingWindow.AnnotationFloatingWindow {
    private String tag = "CropFloatingWindow";
    @GT.Annotations.GT_View(R.id.civImg)
    CropImageView civImg;
    @GT.Annotations.GT_View(R.id.ivImg)
    ImageView ivImg;
    @GT.Annotations.GT_View(R.id.linCrop)
    LinearLayout linCrop;
    @GT.Annotations.GT_View(R.id.ivIcon)
    ImageView ivIcon;
    @GT.Annotations.GT_View(R.id.tvConfirm)
    TextView tvConfirm;
    @GT.Annotations.GT_View(R.id.ivClose)
    ImageView ivClose;

    private Bitmap mBitmap;
    private int left, top, right, bottom,
            width, height;

    @Override
    protected void initView(View view) {
        super.initView(view);
        Log.e(tag, "initView");
        setDrag(true);//设置可拖动
    }

    @Override
    public void onCreate() {
        super.onCreate();
        updateView(getWidth(), getHeight());
        left = (int) SpUtils.getInstance().get(Constants.LEFT, 0);
        top = (int) SpUtils.getInstance().get(Constants.TOP, 0);
        right = (int) SpUtils.getInstance().get(Constants.RIGHT, 0);
        bottom = (int) SpUtils.getInstance().get(Constants.BOTTOM, 0);
        width = (int) SpUtils.getInstance().get(Constants.CROP_WIDTH, 200);
        height = (int) SpUtils.getInstance().get(Constants.CROP_HEIGHT, 200);
        createVirtualEnvironment();
        new Handler().postDelayed(() -> {
            startVirtual();
            new Handler().postDelayed(() -> {
                linCrop.setVisibility(View.GONE);
                startCapture();
            }, 500);
        }, 500);
    }

    @GT.Annotations.GT_Click({R.id.ivIcon, R.id.tvConfirm, R.id.ivClose})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.ivIcon:
                GT.startFloatingWindow(this, MainFloatingWindow.class);
                finish();
                break;
//            case R.id.tvCrop:
//                //截图
//                startCapture();
//                break;
            case R.id.tvConfirm:
                //确认识别区域
                SpUtils.getInstance().put(Constants.LEFT, left);
                SpUtils.getInstance().put(Constants.TOP, top);
                SpUtils.getInstance().put(Constants.RIGHT, right);
                SpUtils.getInstance().put(Constants.BOTTOM, bottom);
                SpUtils.getInstance().put(Constants.CROP_WIDTH, width);
                SpUtils.getInstance().put(Constants.CROP_HEIGHT, height);
                try {
                    final Bitmap croppedImage = civImg.getCropImage();
                    SpUtils.getInstance().saveBitmap(croppedImage);
                    ivImg.setImageBitmap(croppedImage);
                }catch (Exception e){
                    Log.e(tag,"e = "+e.getMessage());
                }
                GT.startFloatingWindow(this, CashierFloatingWindow.class);
                finish();
                break;
            case R.id.ivClose:
                //关闭
                GT.startFloatingWindow(this, CashierFloatingWindow.class);
                finish();
                break;
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        tearDownMediaProjection();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        // 将Service设置为前台服务
        createNotificationChannel();
        return START_STICKY;
    }

    /**
     * 创建前台通知
     */
    private void createNotificationChannel() {
        //Android8.0
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationManager notificationManager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
            NotificationChannel channel = new NotificationChannel(String.valueOf(Constants.notification_id),
                    Constants.notification_name,
                    NotificationManager.IMPORTANCE_MIN);
            channel.setSound(null, null); // 关闭声音
            channel.enableVibration(false); // 禁止震动
            notificationManager.createNotificationChannel(channel);
        }

        Notification.Builder builder = new Notification.Builder(this); //获取一个Notification构造器
        Intent nfIntent = new Intent(this, LauncherActivity.class); //点击后跳转的界面，可以设置跳转数据

        //普通notification适配
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            builder.setChannelId(String.valueOf(Constants.notification_id));
        }

        builder.setContentIntent(PendingIntent.getActivity(this, 0, nfIntent, 0)) // 设置PendingIntent
                .setLargeIcon(BitmapFactory.decodeResource(this.getResources(), R.mipmap.ic_launcher)) // 设置下拉列表中的图标(大图标)
                .setSmallIcon(R.mipmap.ic_launcher) // 设置状态栏内的小图标
                .setContentText("is running......") // 设置上下文内容
                .setVibrate(null)
                .setSound(null)
                .setLights(0, 0, 0)
                .setWhen(System.currentTimeMillis()); // 设置该通知发生的时间

        Notification notification = builder.build(); // 获取构建好的Notification
        startForeground(Constants.notification_id, notification);
    }

    /**********************************截屏start*********************************/

    public static MediaProjectionManager mMediaProjectionManager = null;
    private int windowWidth = 0,
            windowHeight = 0,
            mScreenDensity = 0;
    private DisplayMetrics metrics = null;
    private ImageReader mImageReader = null;

    private MediaProjection mMediaProjection = null;
    public static Intent mResultData = null;
    public static int mResultCode = 0;
    private VirtualDisplay mVirtualDisplay = null;

    /**
     * 创建截屏
     */
    @SuppressLint("WrongConstant")
    private void createVirtualEnvironment() {
        mMediaProjectionManager = ((BaseApplication) getApplication()).getMediaProjectionManager();
        if (mMediaProjectionManager == null) {
            mMediaProjectionManager = (MediaProjectionManager) getApplication().getSystemService(Context.MEDIA_PROJECTION_SERVICE);
        }
        Log.e(tag, "mMediaProjectionManager0 = " + mMediaProjectionManager);
        windowWidth = getWindowManager().getDefaultDisplay().getWidth();
        windowHeight = getWindowManager().getDefaultDisplay().getHeight();
        metrics = new DisplayMetrics();
        getWindowManager().getDefaultDisplay().getMetrics(metrics);
        mScreenDensity = metrics.densityDpi;
        mImageReader = ImageReader.newInstance(windowWidth, windowHeight, 0x1, 2); //ImageFormat.RGB_565
    }

    public void startVirtual() {
        if (mMediaProjection != null) {
            virtualDisplay();
        } else {
            setUpMediaProjection();
            virtualDisplay();
        }
    }

    public void setUpMediaProjection() {
        mResultData = ((BaseApplication) getApplication()).getIntent();
        mResultCode = ((BaseApplication) getApplication()).getResult();
        Log.e(tag, "mMediaProjectionManager = " + mMediaProjectionManager);
        try {
            if (mMediaProjectionManager != null) {
                mMediaProjection = mMediaProjectionManager.getMediaProjection(mResultCode, mResultData);
            }
        } catch (SecurityException e) {
            Log.e(tag, "e = " + e.getMessage());
        }
    }

    private void virtualDisplay() {
        Log.e(tag, "mMediaProjection = " + mMediaProjection);
        if (mMediaProjection != null) {
            mVirtualDisplay = mMediaProjection.createVirtualDisplay("screen-mirror",
                    windowWidth, windowHeight, mScreenDensity, DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR,
                    mImageReader.getSurface(), null, null);
        }
    }

    private void startCapture() {
        Image image = mImageReader.acquireLatestImage();
        Log.e(tag, "image = " + image);
        if (image != null) {
            int width = image.getWidth();
            int height = image.getHeight();
            final Image.Plane[] planes = image.getPlanes();
            final ByteBuffer buffer = planes[0].getBuffer();
            int pixelStride = planes[0].getPixelStride();
            int rowStride = planes[0].getRowStride();
            int rowPadding = rowStride - pixelStride * width;
            Bitmap bitmap = Bitmap.createBitmap(width + rowPadding / pixelStride, height, Bitmap.Config.ARGB_8888);
            bitmap.copyPixelsFromBuffer(buffer);
            bitmap = Bitmap.createBitmap(bitmap, 0, 0, width, height);
            image.close();
            Message message = new Message();
            message.what = 0;
            message.obj = bitmap;
            myHandler.sendMessage(message);
        } else {
            linCrop.setVisibility(View.VISIBLE);
        }
    }

    private void tearDownMediaProjection() {
        if (mMediaProjection != null) {
            mMediaProjection.stop();
            mMediaProjection = null;
        }
        stopVirtual();
    }

    private void stopVirtual() {
        if (mVirtualDisplay == null) {
            return;
        }
        mVirtualDisplay.release();
        mVirtualDisplay = null;
    }

    /**********************************截屏end*********************************/


    @SuppressLint("HandlerLeak")
    Handler myHandler = new Handler() {
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case 0:
                    linCrop.setVisibility(View.VISIBLE);
                    mBitmap = (Bitmap) msg.obj;
                    if (mBitmap != null) {
                        civImg.setDrawable(new BitmapDrawable(getResources(), mBitmap),
                                width, height, left, top);
                        civImg.setListener((lefts, tops, rights, bottoms, widths, heights) -> {
                            Log.e(tag, "left = " + left + " top = " + top + " right = " + right + " bottom = " + bottom + " width = " + width + " height = " + height);
                            left = lefts;
                            top = tops;
                            right = rights;
                            bottom = bottoms;
                            width = widths;
                            height = heights;
                        });
                    }
                    break;
            }
            super.handleMessage(msg);
        }
    };


}
