package com.yxl.cashier.helper.utils;

import android.content.Context;
import android.speech.tts.TextToSpeech;
import android.speech.tts.UtteranceProgressListener;
import android.util.Log;

import com.yxl.commonlibrary.base.BaseApplication;
import com.yxl.commonlibrary.utils.ToastUtils;

import java.util.Locale;

import kotlin.jvm.Volatile;

public class SystemTTS extends UtteranceProgressListener implements TTS {
    private Context context;

    public SystemTTS(Context context) {
        this.context = context;
        textToSpeech = new TextToSpeech(context, status -> {
            Log.e("111111","status = "+status);
            //系统语音初始化成功
            if (status == TextToSpeech.SUCCESS) {
                int languageResult = textToSpeech.setLanguage(Locale.CHINA);
                textToSpeech.setPitch(1.0f);//设置音调，值越大声音越尖（女生），值越小则变成男声,1.0是常规
                textToSpeech.setSpeechRate(1.0f);
                textToSpeech.setOnUtteranceProgressListener(SystemTTS.this);
                //textToSpeech.setOnUtteranceCompletedListener(this)
                if (languageResult == TextToSpeech.LANG_MISSING_DATA || languageResult == TextToSpeech.LANG_NOT_SUPPORTED) {
                    //系统不支持中文播报
                    isSuccess = false;
                }
            }
        });
    }

    TextToSpeech textToSpeech;
    boolean isSuccess = true;

    @Volatile
    private static volatile SystemTTS instance;

    public static SystemTTS getInstance(Context context) {
        if (instance == null) {
            synchronized (SystemTTS.class) {
                if (instance == null) {
                    instance = new SystemTTS(context);
                }
            }
        }
        return instance;
    }

    @Override
    public void onStart(String utteranceId) {

    }

    @Override
    public void onDone(String utteranceId) {

    }

    @Override
    public void onError(String utteranceId) {

    }

    @Override
    public void playText(String playText) {
        if (!isSuccess) {
            ToastUtils.getInstance(BaseApplication.getInstance()).showMessage("系统不支持中文播报");
            return;
        }
        textToSpeech.speak(playText, TextToSpeech.QUEUE_ADD, null, null);
    }

    @Override
    public void stopSpeak() {
        textToSpeech.stop();
    }
}
