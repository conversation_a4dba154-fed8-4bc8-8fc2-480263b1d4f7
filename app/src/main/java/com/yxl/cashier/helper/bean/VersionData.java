package com.yxl.cashier.helper.bean;

import java.io.Serializable;

/**
 * 最新版本（实体类）
 */
public class VersionData implements Serializable {

    /**
     * code : 200
     * msg : 操作成功
     * data : {"id":4,"appId":1,"appCode":"PC_PLUGIN_CBD","systemType":1,"version":"v1.1.1","forceUpgrade":1,"introduce":"软件升级","remark":"软件升级","fileUrl":"http://document.buyhoo.cc/platform-test/platform/2024/1/5/7d4da641376f43df93de19c32988f8da.apk","createByName":null,"createTime":"2024-02-05 15:50:17","modifyByName":null,"modifyTime":"2024-02-05 15:50:17"}
     */

    private int code;
    private String msg;
    private DataBean data;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public DataBean getData() {
        return data;
    }

    public void setData(DataBean data) {
        this.data = data;
    }

    public static class DataBean {
        /**
         * id : 4
         * appId : 1
         * appCode : PC_PLUGIN_CBD
         * systemType : 1
         * version : v1.1.1
         * forceUpgrade : 1
         * introduce : 软件升级
         * remark : 软件升级
         * fileUrl : http://document.buyhoo.cc/platform-test/platform/2024/1/5/7d4da641376f43df93de19c32988f8da.apk
         * createByName : null
         * createTime : 2024-02-05 15:50:17
         * modifyByName : null
         * modifyTime : 2024-02-05 15:50:17
         */

        private int id;
        private int appId;
        private String appCode;
        private int systemType;//系统版本 1.Android 2.ios 3.Windows
        private String version;//系统版本号
        private int forceUpgrade;//是否强制升级 0.否 1.是
        private String introduce;//升级简介
        private String remark;//升级内容
        private String fileUrl;//文件地址
        private String createByName;//创建人
        private String createTime;//创建时间
        private String modifyByName;//修改人
        private String modifyTime;//修改时间
        private int versionCode;//版本号

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public int getAppId() {
            return appId;
        }

        public void setAppId(int appId) {
            this.appId = appId;
        }

        public String getAppCode() {
            return appCode;
        }

        public void setAppCode(String appCode) {
            this.appCode = appCode;
        }

        public int getSystemType() {
            return systemType;
        }

        public void setSystemType(int systemType) {
            this.systemType = systemType;
        }

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }

        public int getForceUpgrade() {
            return forceUpgrade;
        }

        public void setForceUpgrade(int forceUpgrade) {
            this.forceUpgrade = forceUpgrade;
        }

        public String getIntroduce() {
            return introduce;
        }

        public void setIntroduce(String introduce) {
            this.introduce = introduce;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public String getFileUrl() {
            return fileUrl;
        }

        public void setFileUrl(String fileUrl) {
            this.fileUrl = fileUrl;
        }

        public String getCreateByName() {
            return createByName;
        }

        public void setCreateByName(String createByName) {
            this.createByName = createByName;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getModifyByName() {
            return modifyByName;
        }

        public void setModifyByName(String modifyByName) {
            this.modifyByName = modifyByName;
        }

        public String getModifyTime() {
            return modifyTime;
        }

        public void setModifyTime(String modifyTime) {
            this.modifyTime = modifyTime;
        }

        public int getVersionCode() {
            return versionCode;
        }

        public void setVersionCode(int versionCode) {
            this.versionCode = versionCode;
        }
    }
}
