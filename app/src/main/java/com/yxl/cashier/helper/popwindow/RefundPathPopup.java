package com.yxl.cashier.helper.popwindow;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.view.View;
import android.widget.PopupWindow;

import com.yxl.cashier.helper.R;

/**
 * Describe:popup（退款路径说明）
 * Created by jingang on 2023/5/30
 */
@SuppressLint("NonConstantResourceId")
public class RefundPathPopup extends PopupWindow {
    @SuppressLint("SetTextI18n")
    public RefundPathPopup(Context context) {
        super(context);
        View view = View.inflate(context, R.layout.popup_refund_path, null);
        setContentView(view);
        setWidth(560);
        // 设置PopupWindow的背景透明
        setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        //设置可以触摸弹出框以外的区域
        setOutsideTouchable(true);
    }

//    public static void showDialog(Context context, int width, View viewShow) {
//        popupWindow = new RefundPathPopup(context);
//        popupWindow.setWidth(560);
//        // 设置PopupWindow的背景透明
//        popupWindow.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
//        //设置可以触摸弹出框以外的区域
//        popupWindow.setOutsideTouchable(true);
//        //放在具体控件下方
////        popupWindow.showAsDropDown(viewShow);
//        popupWindow.showAsDropDown(viewShow, -(width - 560 - 120) / 2, 0, Gravity.CENTER_HORIZONTAL);
//    }

}
