package com.yxl.cashier.helper.bean;

import java.io.Serializable;

/**
 * Describe:筛选条件（实体类）
 * Created by jingang on 2024/1/17
 */
public class ConditionData implements Serializable {
    private int value;
    private String name;

    public ConditionData() {
    }

    public ConditionData(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
