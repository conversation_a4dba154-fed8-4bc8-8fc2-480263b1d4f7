package com.yxl.cashier.helper.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.cashier.helper.Interface.OnItemClickListener;
import com.yxl.cashier.helper.R;
import com.yxl.cashier.helper.bean.DailySummaryData;
import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;
import com.yxl.commonlibrary.utils.DFUtils;

/**
 * Describe:汇总列表（适配器）
 * Created by jingang on 2023/12/27
 */
public class TotalAdapter extends BaseAdapter<DailySummaryData.PayTypeListBean> {
    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public TotalAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_total;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        if (onItemClickListener != null) {
            holder.itemView.setOnClickListener(v -> onItemClickListener.onItemClick(v, position));
        }
        LinearLayout lin = holder.getView(R.id.linItem);
        ImageView ivType = holder.getView(R.id.ivItemType);
        TextView tvType, tvRealTotal, tvTotal, tvDiscount, tvRefund;
        tvType = holder.getView(R.id.tvItemType);
        tvRealTotal = holder.getView(R.id.tvItemRealTotal);
        tvTotal = holder.getView(R.id.tvItemTotal);
        tvDiscount = holder.getView(R.id.tvItemDiscount);
        tvRefund = holder.getView(R.id.tvItemRefund);

        if (position % 2 == 0) {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.white));
        } else {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.color_f7));
        }

        //支付方式：2-支付宝,3-微信,8-金圈支付,13-云闪付支付
        //收款方式：1.现金 8.小程序 13金圈支付
        switch (mDataList.get(position).getPayType()) {
//            case 2:
//                ivType.setImageResource(R.mipmap.ic_pay_type002);
//                tvType.setText("支付宝支付");
//                break;
//            case 3:
//                ivType.setImageResource(R.mipmap.ic_pay_type001);
//                tvType.setText("微信支付");
//                break;
//            case 8:
//                ivType.setImageResource(R.mipmap.ic_pay_type003);
//                tvType.setText("金圈支付");
//                break;
//            case 13:
//                ivType.setImageResource(R.mipmap.ic_pay_type004);
//                tvType.setText("云闪付支付");
//                break;
            case 1:
                ivType.setImageResource(R.mipmap.ic_payment001);
                tvType.setText("现金");
                break;
            case 8:
                ivType.setImageResource(R.mipmap.ic_payment002);
                tvType.setText("小程序");
                break;
            case 13:
                ivType.setImageResource(R.mipmap.ic_payment003);
                tvType.setText("金圈");
                break;
            default:
                ivType.setImageResource(0);
                tvType.setText("");
                break;
        }
        tvRealTotal.setText(DFUtils.getNum2(mDataList.get(position).getActualReceiptAmount()));
        tvTotal.setText(mDataList.get(position).getTransactionCount() + "笔" + DFUtils.getNum2(mDataList.get(position).getTransactionAmount()) + "元");
        tvDiscount.setText(mDataList.get(position).getDiscountCount() + "笔" + DFUtils.getNum2(mDataList.get(position).getDiscountAmount()) + "元");
        tvRefund.setText(mDataList.get(position).getRefundCount() + "笔" + DFUtils.getNum2(mDataList.get(position).getRefundAmount()) + "元");
    }
}
