package com.yxl.cashier.helper.bean;

import android.graphics.Bitmap;

/**
 * Describe:
 * Created by jingang on 2023/12/3
 */
public class EventData {
    private String msg;
    private Bitmap bitmap;
    private String barcode;//扫码枪扫码结果

    public EventData() {
    }

    public EventData(String msg, Bitmap bitmap) {
        this.msg = msg;
        this.bitmap = bitmap;
    }

    public EventData(String msg, String barcode) {
        this.msg = msg;
        this.barcode = barcode;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Bitmap getBitmap() {
        return bitmap;
    }

    public void setBitmap(Bitmap bitmap) {
        this.bitmap = bitmap;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }
}
