package com.yxl.cashier.helper.bean;

import java.io.Serializable;
import java.util.List;

/**
 * Describe:汇总-日统计（实体类）
 * Created by jingang on 2024/1/24
 */
public class DailySummaryData implements Serializable {
    /**
     * total : {"actualReceiptAmount":0,"transactionCount":0,"transactionAmount":0,"discountCount":0,"discountAmount":0,"refundCount":0,"refundAmount":0}
     * payTypeList : [{"payType":0,"actualReceiptAmount":0,"transactionCount":0,"transactionAmount":0,"discountCount":0,"discountAmount":0,"refundCount":0,"refundAmount":0}]
     */

    private TotalBean total;
    private List<PayTypeListBean> payTypeList;

    public TotalBean getTotal() {
        return total;
    }

    public void setTotal(TotalBean total) {
        this.total = total;
    }

    public List<PayTypeListBean> getPayTypeList() {
        return payTypeList;
    }

    public void setPayTypeList(List<PayTypeListBean> payTypeList) {
        this.payTypeList = payTypeList;
    }

    public static class TotalBean {
        /**
         * actualReceiptAmount : 0
         * transactionCount : 0
         * transactionAmount : 0
         * discountCount : 0
         * discountAmount : 0
         * refundCount : 0
         * refundAmount : 0
         */

        private double actualReceiptAmount;//实际收款金额
        private int transactionCount;//交易笔数
        private double transactionAmount;//交易金额
        private int discountCount;//优惠笔数
        private double discountAmount;//优惠金额
        private int refundCount;//退款笔数
        private double refundAmount;//退款金额

        public double getActualReceiptAmount() {
            return actualReceiptAmount;
        }

        public void setActualReceiptAmount(double actualReceiptAmount) {
            this.actualReceiptAmount = actualReceiptAmount;
        }

        public int getTransactionCount() {
            return transactionCount;
        }

        public void setTransactionCount(int transactionCount) {
            this.transactionCount = transactionCount;
        }

        public double getTransactionAmount() {
            return transactionAmount;
        }

        public void setTransactionAmount(double transactionAmount) {
            this.transactionAmount = transactionAmount;
        }

        public int getDiscountCount() {
            return discountCount;
        }

        public void setDiscountCount(int discountCount) {
            this.discountCount = discountCount;
        }

        public double getDiscountAmount() {
            return discountAmount;
        }

        public void setDiscountAmount(double discountAmount) {
            this.discountAmount = discountAmount;
        }

        public int getRefundCount() {
            return refundCount;
        }

        public void setRefundCount(int refundCount) {
            this.refundCount = refundCount;
        }

        public double getRefundAmount() {
            return refundAmount;
        }

        public void setRefundAmount(double refundAmount) {
            this.refundAmount = refundAmount;
        }
    }

    public static class PayTypeListBean {
        /**
         * payType : 0
         * actualReceiptAmount : 0
         * transactionCount : 0
         * transactionAmount : 0
         * discountCount : 0
         * discountAmount : 0
         * refundCount : 0
         * refundAmount : 0
         */

        private int payType;//支付方式 改为收款方式：1.现金 8.小程序 13金圈支付
        private double actualReceiptAmount;//实际收款金额
        private int transactionCount;//交易笔数
        private double transactionAmount;//交易金额
        private int discountCount;//优惠笔数
        private double discountAmount;//优惠金额
        private int refundCount;//退款笔数
        private double refundAmount;//退款金额

        public int getPayType() {
            return payType;
        }

        public void setPayType(int payType) {
            this.payType = payType;
        }

        public double getActualReceiptAmount() {
            return actualReceiptAmount;
        }

        public void setActualReceiptAmount(double actualReceiptAmount) {
            this.actualReceiptAmount = actualReceiptAmount;
        }

        public int getTransactionCount() {
            return transactionCount;
        }

        public void setTransactionCount(int transactionCount) {
            this.transactionCount = transactionCount;
        }

        public double getTransactionAmount() {
            return transactionAmount;
        }

        public void setTransactionAmount(double transactionAmount) {
            this.transactionAmount = transactionAmount;
        }

        public int getDiscountCount() {
            return discountCount;
        }

        public void setDiscountCount(int discountCount) {
            this.discountCount = discountCount;
        }

        public double getDiscountAmount() {
            return discountAmount;
        }

        public void setDiscountAmount(double discountAmount) {
            this.discountAmount = discountAmount;
        }

        public int getRefundCount() {
            return refundCount;
        }

        public void setRefundCount(int refundCount) {
            this.refundCount = refundCount;
        }

        public double getRefundAmount() {
            return refundAmount;
        }

        public void setRefundAmount(double refundAmount) {
            this.refundAmount = refundAmount;
        }
    }
}
