package com.yxl.cashier.helper;

import android.Manifest;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.media.projection.MediaProjectionManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;

import com.allenliu.versionchecklib.core.http.HttpParams;
import com.allenliu.versionchecklib.v2.AllenVersionChecker;
import com.allenliu.versionchecklib.v2.builder.DownloadBuilder;
import com.allenliu.versionchecklib.v2.builder.NotificationBuilder;
import com.allenliu.versionchecklib.v2.builder.UIData;
import com.allenliu.versionchecklib.v2.callback.CustomDownloadFailedListener;
import com.allenliu.versionchecklib.v2.callback.CustomDownloadingDialogListener;
import com.allenliu.versionchecklib.v2.callback.CustomVersionDialogListener;
import com.allenliu.versionchecklib.v2.callback.ForceUpdateListener;
import com.allenliu.versionchecklib.v2.callback.RequestVersionListener;
import com.google.gson.Gson;
import com.gsls.gt.GT;
import com.yxl.cashier.helper.bean.VersionData;
import com.yxl.cashier.helper.dialog.BaseDialog;
import com.yxl.cashier.helper.gt.CashierFloatingWindow;
import com.yxl.cashier.helper.utils.PermissionUtils;
import com.yxl.commonlibrary.Constants;
import com.yxl.commonlibrary.base.BaseApplication;
import com.yxl.commonlibrary.utils.FileUtils;
import com.yxl.commonlibrary.utils.PackageUtils;
import com.yxl.commonlibrary.utils.SpUtils;

/**
 * 白页
 */
public class LauncherActivity extends GT.GT_Activity.AnnotationActivity {
    private String tag = "LauncherActivity";

    @Override
    protected void initView(Bundle savedInstanceState) {
        super.initView(savedInstanceState);
        GT.build(this);
        mMediaProjectionManager = (MediaProjectionManager) getApplication().getSystemService(Context.MEDIA_PROJECTION_SERVICE);
        checkPermission();
    }

    /**********************版本更新start************************/
    private VersionData versionData;

    /**
     * 更新版本
     */
    private void checkUpgrade() {
        HttpParams map = new HttpParams();
        map.put("app_id", "2");
        map.put("app_type", "1");
        DownloadBuilder builder = AllenVersionChecker
                .getInstance()
                .requestVersion()
                .setRequestUrl(ZURL.getVersion())
                .setRequestParams(map)
                .request(new RequestVersionListener() {
                    @Nullable
                    @Override
                    public UIData onRequestVersionSuccess(DownloadBuilder downloadBuilder, String result) {
                        Log.e(tag, "version success = " + result);
                        versionData = new Gson().fromJson(result, VersionData.class);
                        if (versionData.getCode() == Constants.SUCCESS_CODE) {
                            if (versionData.getData() == null) return null;
//                            //最新版本>当前版本
//                            if (versionData.getData().getVersionCode() > PackageUtils.getPackageCode(LauncherActivity.this)) {
//                                //是否强制更新 0.否 1.是
//                                if (versionData.getData().getForceUpgrade() == 1) {
//                                    downloadBuilder.setForceUpdateListener(() -> {
//                                        finish();
//                                    });
//                                    return crateUIData();
//                                } else {
//                                    String time = String.valueOf(SpUtils.getInstance().get(Constants.LAST_UPDATE, "0"));
//                                    long lastTime = 0;
//                                    if (!TextUtils.isEmpty(time)) {
//                                        lastTime = Long.parseLong(time);
//                                    }
//                                    long currentTime = System.currentTimeMillis();
//                                    if (currentTime - lastTime > 1000 * 60 * 60 * 24) {//一天提醒一次
//                                        SpUtils.getInstance().put(Constants.LAST_UPDATE, String.valueOf(System.currentTimeMillis()));
//                                        return crateUIData();
//                                    }
//                                }
//                            }
                            String time = String.valueOf(SpUtils.getInstance().get(Constants.LAST_UPDATE, "0"));
                            long lastTime = 0;
                            if (!TextUtils.isEmpty(time)) {
                                lastTime = Long.parseLong(time);
                            }
                            long currentTime = System.currentTimeMillis();
                            if (currentTime - lastTime > 1000 * 60 * 60 * 24) {//一天提醒一次
                                SpUtils.getInstance().put(Constants.LAST_UPDATE, String.valueOf(System.currentTimeMillis()));
                                //是否强制更新 0.否 1.是
                                if (versionData.getData().getForceUpgrade() == 1) {
                                    downloadBuilder.setForceUpdateListener(() -> {
                                        finish();
                                    });
                                }
                                return crateUIData();
                            }
                        }
                        return null;
                    }

                    @Override
                    public void onRequestVersionFailure(String message) {
                        Log.e(tag, "version error = " + message);
                    }
                });
        builder.setNotificationBuilder(createCustomNotification());
//        builder.setDownloadAPKPath(this.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS).getPath() + "/");
        builder.setDownloadAPKPath(FileUtils.getPath(this));
        builder.setCustomVersionDialogListener(createCustomDialog());
        builder.setCustomDownloadingDialogListener(createCustomDownloadingDialog());
        builder.setCustomDownloadFailedListener(createCustomDownloadFailedDialog());
        builder.setForceRedownload(true);
        builder.executeMission(this);
    }

    /**
     * 自定义下载中对话框，下载中会连续回调此方法 updateUI
     * 务必用库传回来的context 实例化你的dialog
     *
     * @return
     */
    private CustomDownloadingDialogListener createCustomDownloadingDialog() {
        return new CustomDownloadingDialogListener() {
            @Override
            public Dialog getCustomDownloadingDialog(Context context, int progress, UIData versionBundle) {
                BaseDialog baseDialog = new BaseDialog(context, R.style.dialog_style, R.layout.dialog_download);
                return baseDialog;
            }

            @Override
            public void updateUI(Dialog dialog, int progress, UIData versionBundle) {
                TextView tvProgress = dialog.findViewById(R.id.tvProgress);
                ProgressBar progressBar = dialog.findViewById(R.id.pb);
                progressBar.setProgress(progress);
                tvProgress.setText(getString(R.string.versionchecklib_progress, progress));
            }
        };
    }

    /**
     * 务必用库传回来的context 实例化你的dialog
     *
     * @return
     */
    private CustomDownloadFailedListener createCustomDownloadFailedDialog() {
        return (context, versionBundle) -> {
            BaseDialog baseDialog = new BaseDialog(context, R.style.dialog_style, R.layout.dialog_download_failed);
            return baseDialog;
        };
    }

    private CustomVersionDialogListener createCustomDialog() {
        return (context, versionBundle) -> {
            BaseDialog dialog = new BaseDialog(context, R.style.dialog_style, R.layout.dialog_download_two);
            dialog.setCancelable(false);

            TextView tvTitle, tvContent;
            tvTitle = dialog.findViewById(R.id.tv_title);
            tvContent = dialog.findViewById(R.id.tv_msg);

            tvTitle.setText(versionBundle.getTitle());
            tvContent.setText(versionBundle.getContent());
            dialog.setCanceledOnTouchOutside(false);
            return dialog;
        };
    }

    private NotificationBuilder createCustomNotification() {
        return NotificationBuilder.create()
                .setRingtone(true)
                .setIcon(R.mipmap.ic_launcher)
                .setTicker("custom_ticker")
                .setContentTitle("金圈收银助手升级")
                .setContentText("升级中");
    }

    /**
     * @return
     * @important 使用请求版本功能，可以在这里设置downloadUrl
     * 这里可以构造UI需要显示的数据
     * UIData 内部是一个Bundle
     */
    private UIData crateUIData() {
        UIData uiData = UIData.create();
        uiData.setTitle(versionData.getData().getIntroduce());
        uiData.setDownloadUrl(versionData.getData().getFileUrl());
        uiData.setContent(versionData.getData().getRemark());
        return uiData;
    }

    /**********************版本更新end************************/


    //权限：读写(13一下)
    public static final String[] PERMISSION_READ = {
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
    };

    //权限：拍照、选择图片(13及以上)
    @RequiresApi(api = Build.VERSION_CODES.TIRAMISU)
    public static final String[] PERMISSION_READ_13 = {
            Manifest.permission.READ_MEDIA_IMAGES,
            Manifest.permission.READ_MEDIA_AUDIO,
            Manifest.permission.READ_MEDIA_VIDEO
    };

    /**
     * 权限请求（读写）
     */
    private void checkPermission() {
        String[] PERMISSION;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            PERMISSION = PERMISSION_READ_13;
        } else {
            PERMISSION = PERMISSION_READ;
        }
        if (PermissionUtils.checkPermissionsGroup(this, PERMISSION)) {
            canDrawOverlays();
        } else {
            PermissionUtils.requestPermissions(this, PERMISSION, Constants.PERMISSION);
        }
    }

    /**
     * 悬浮窗权限
     */
    private void canDrawOverlays() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !Settings.canDrawOverlays(this)) {
            Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                    Uri.parse("package:" + getPackageName()));
            startActivityForResult(intent, Constants.PERMISSION_FLOAT);
        } else {
            checkUpgrade();
            startIntent(CashierFloatingWindow.class);
        }
    }

    /***********************Service-start***************************/
    //截屏
    private int result = 0;
    private Intent intent = null;
    private MediaProjectionManager mMediaProjectionManager;
    private Class<?> cls;

    /**
     * 启动服务
     */
    public void startIntent(Class<?> cls) {
        this.cls = cls;
        if (intent != null && result != 0) {
            ((BaseApplication) getApplication()).setResult(result);
            ((BaseApplication) getApplication()).setIntent(intent);
            GT.startFloatingWindow(this, cls);
//            finish();
            moveTaskToBack(true);
        } else {
            startActivityForResult(mMediaProjectionManager.createScreenCaptureIntent(), Constants.PERMISSION_PROJECTION);
            ((BaseApplication) getApplication()).setMediaProjectionManager(mMediaProjectionManager);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        boolean hasPermissionDismiss = false;
        switch (requestCode) {
            case Constants.PERMISSION:
                for (int grantResult : grantResults) {
                    if (grantResult == -1) {
                        hasPermissionDismiss = true;
                        break;
                    }
                }
                //如果有权限没有被允许
                if (hasPermissionDismiss) {
                    GT.toast("因权限未开启，该功能无法使用，请去设置中开启。");
                    finish();
                } else {
                    canDrawOverlays();
                }
                break;
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case Constants.PERMISSION_FLOAT:
                //悬浮窗
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && Settings.canDrawOverlays(this)) {
                    canDrawOverlays();
                } else {
                    finish();
                }
                break;
            case Constants.PERMISSION_PROJECTION:
                if (resultCode == RESULT_OK) {
                    result = resultCode;
                    intent = data;
                    ((BaseApplication) getApplication()).setResult(resultCode);
                    ((BaseApplication) getApplication()).setIntent(data);
                    GT.startFloatingWindow(this, cls);
//                    finish();
                    moveTaskToBack(true);
                    break;
                } else {
                    finish();
                }
        }
    }

}
