package com.yxl.cashier.helper.popwindow;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.os.Build;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.TextView;
import android.widget.Toast;

import com.yxl.cashier.helper.R;
import com.yxl.cashier.helper.view.pickerview.DateTimeEntity;
import com.yxl.cashier.helper.view.pickerview.DateTimeWheelLayout;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Describe:选择日期
 * Created by jingang on 2024/1/2
 */
@SuppressLint({"StaticFieldLeak", "NonConstantResourceId"})
public class DatePopupWindow extends PopupWindow implements View.OnClickListener {
    private String tag = "DatePopupWindow";
    private static Context mContext;
    private static View viewIcon;
    private final Animation openAnim, closeAnim;

    private TextView tvCancel, tvConfirm, tvDateStart, tvDateEnd;
    private LinearLayout linStartTime, linEndTime;
    private View vDateStart, vDateEnd;
    private DateTimeWheelLayout wheelLayout;

    private static int type;//0.开始 1.结束
    private static String startDate, endDate;

    public DatePopupWindow(Context context) {
        super(context);
        View view = View.inflate(context, R.layout.pop_date, null);
        setContentView(view);
        tvCancel = view.findViewById(R.id.tvDialogCancel);
        tvConfirm = view.findViewById(R.id.tvDialogConfirm);
        linStartTime = view.findViewById(R.id.linDialogDateStart);
        tvDateStart = view.findViewById(R.id.tvDialogDateStart);
        vDateStart = view.findViewById(R.id.vDialogDateStart);
        linEndTime = view.findViewById(R.id.linDialogDateEnd);
        tvDateEnd = view.findViewById(R.id.tvDialogDateEnd);
        vDateEnd = view.findViewById(R.id.vDialogDateEnd);
        wheelLayout = view.findViewById(R.id.wheelLayout);
        tvCancel.setOnClickListener(this);
        tvConfirm.setOnClickListener(this);
        linStartTime.setOnClickListener(this);
        linEndTime.setOnClickListener(this);

        tvDateStart.setText(startDate);
        tvDateEnd.setText(endDate);

        if (type == 0) {
            tvDateStart.setTextColor(mContext.getResources().getColor(R.color.blue));
            vDateStart.setBackgroundColor(mContext.getResources().getColor(R.color.blue));
            tvDateEnd.setTextColor(mContext.getResources().getColor(R.color.color_999));
            vDateEnd.setBackgroundColor(mContext.getResources().getColor(R.color.color_e1e2e3));
        } else {
            tvDateStart.setTextColor(mContext.getResources().getColor(R.color.color_999));
            vDateStart.setBackgroundColor(mContext.getResources().getColor(R.color.color_e1e2e3));
            tvDateEnd.setTextColor(mContext.getResources().getColor(R.color.blue));
            vDateEnd.setBackgroundColor(mContext.getResources().getColor(R.color.blue));
        }
        wheelLayout.setDefaultValue(DateTimeEntity.today()); //当前日期
        wheelLayout.setSelectedTextColor(mContext.getResources().getColor(R.color.black));//选中字体颜色
        wheelLayout.setSelectedTextSize(12 * mContext.getResources().getDisplayMetrics().scaledDensity);//选中字体大小
        wheelLayout.setTextColor(mContext.getResources().getColor(R.color.color_999));//字体颜色
        wheelLayout.setTextSize(11 * mContext.getResources().getDisplayMetrics().scaledDensity);//字体大小
        wheelLayout.setIndicatorColor(mContext.getResources().getColor(R.color.transparent)); //横线颜色
        wheelLayout.setOnDateTimeSelectedListener((year, month, day) -> {
            Log.e(tag, "year = " + year + "-" + month + "-" + day);
            String month_str, day_str;
            if (month < 10) {
                month_str = "0" + month;
            } else {
                month_str = String.valueOf(month);
            }
            if (day < 10) {
                day_str = "0" + day;
            } else {
                day_str = String.valueOf(day);
            }
            if (type == 0) {
                startDate = year + "-" + month_str + "-" + day_str;
                tvDateStart.setText(startDate);
            } else {
                endDate = year + "-" + month_str + "-" + day_str;
                tvDateEnd.setText(endDate);
            }
        });

        openAnim = AnimationUtils.loadAnimation(context, R.anim.btn_rotate_anim_1);
        closeAnim = AnimationUtils.loadAnimation(context, R.anim.btn_rotate_anim_2);
        openAnim.setFillAfter(true);
        closeAnim.setFillAfter(true);
    }

    public static void showDialog(Context context, View viewIcon, View viewShow,
                                  String startDate, String endDate, String nowDate,
                                  int type,
                                  MyListener listener) {
        DatePopupWindow.mContext = context;
        DatePopupWindow.viewIcon = viewIcon;
        DatePopupWindow.startDate = startDate;
        DatePopupWindow.endDate = endDate;
        DatePopupWindow.type = type;
        DatePopupWindow popupWindow = new DatePopupWindow(context);
        if (popupWindow.isShowing()) {
            popupWindow.dismiss();
        } else {
            popupWindow.setRange(startDate, endDate, nowDate);
            popupWindow.setListener(listener);
            popupWindow.setWidth(300);
            popupWindow.setHeight(ViewGroup.LayoutParams.WRAP_CONTENT);
            //new ColorDrawable(0)即为透明背景
            popupWindow.setBackgroundDrawable(new ColorDrawable(0));
            // 设置动画效果
//        popupWindow.setAnimationStyle(R.style.dialog_anim);
            //设置可以获取焦点
            popupWindow.setFocusable(false);
            //设置可以触摸弹出框以外的区域
            popupWindow.setOutsideTouchable(true);
            //放在具体控件下方
            popupWindow.showAsDropDown(viewShow);
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.tvDialogCancel:
                //取消
                dismiss();
                break;
            case R.id.tvDialogConfirm:
                //确认
                if (TextUtils.isEmpty(startDate)) {
                    Toast.makeText(mContext, "请选择开始时间", Toast.LENGTH_SHORT).show();
                    return;
                }
                if (TextUtils.isEmpty(endDate)) {
                    Toast.makeText(mContext, "请选择结束时间", Toast.LENGTH_SHORT).show();
                    return;
                }
                if (listener != null) {
                    listener.onClick(startDate, endDate);
                }
                dismiss();
                break;
            case R.id.linDialogDateStart:
                //选择开始时间
                if (type != 0) {
                    type = 0;
                    tvDateStart.setTextColor(mContext.getResources().getColor(R.color.blue));
                    vDateStart.setBackgroundColor(mContext.getResources().getColor(R.color.blue));
                    tvDateEnd.setTextColor(mContext.getResources().getColor(R.color.color_999));
                    vDateEnd.setBackgroundColor(mContext.getResources().getColor(R.color.color_e1e2e3));
                    setRange(startDate, endDate, startDate);
                }
                break;
            case R.id.linDialogDateEnd:
                //选择结束时间
                if (type != 1) {
                    type = 1;
                    tvDateStart.setTextColor(mContext.getResources().getColor(R.color.color_999));
                    vDateStart.setBackgroundColor(mContext.getResources().getColor(R.color.color_e1e2e3));
                    tvDateEnd.setTextColor(mContext.getResources().getColor(R.color.blue));
                    vDateEnd.setBackgroundColor(mContext.getResources().getColor(R.color.blue));
                    setRange(startDate, "", endDate);
                }
                break;
        }
    }

    /**
     * 设置日期时间范围
     * <p>
     * 0.查询：开始 1.查询：结束 2.活动：开始 3.活动：结束
     *
     * @param startTime
     * @param endTime
     * @param nowTime
     */
    private void setRange(String startTime, String endTime, String nowTime) {
        DateTimeEntity entityStart, entityEnd, entityNow;

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date dateStart, dateEnd, dateNow;

        /*****************开始日期时间*****************/
        try {
            dateStart = dateFormat.parse(startTime);
            if (dateStart != null) {
                entityStart = new DateTimeEntity();
                if (type == 1) {
                    entityStart.setYear(dateStart.getYear() + 1900);
                } else {
                    entityStart.setYear(dateStart.getYear() + 1890);
                }
                if (dateStart.getMonth() == 0) {
                    entityStart.setMonth(1);
                } else {
                    entityStart.setMonth(dateStart.getMonth() + 1);
                }
                entityStart.setDay(dateStart.getDate());
            } else {
                entityStart = DateTimeEntity.yearOnFuture(-10);
            }
        } catch (Exception e) {
            e.printStackTrace();
            entityStart = DateTimeEntity.yearOnFuture(-10);
        }

        /*****************结束日期时间*****************/
        try {
            dateEnd = dateFormat.parse(endTime);
            if (dateEnd != null) {
                entityEnd = new DateTimeEntity();
                entityEnd.setYear(dateEnd.getYear() + 1900);
                if (dateEnd.getMonth() == 0) {
                    entityEnd.setMonth(1);
                } else {
                    entityEnd.setMonth(dateEnd.getMonth() + 1);
                }
                entityEnd.setDay(dateEnd.getDate());
            } else {
                entityEnd = DateTimeEntity.today();
            }
        } catch (Exception e) {
            e.printStackTrace();
            entityEnd = DateTimeEntity.today();
        }

        /*****************现在日期时间*****************/
        try {
            dateNow = dateFormat.parse(nowTime);
            if (dateNow != null) {
                entityNow = new DateTimeEntity();
                entityNow.setYear(dateNow.getYear() + 1900);
                if (dateNow.getMonth() == 0) {
                    entityNow.setMonth(1);
                } else {
                    entityNow.setMonth(dateNow.getMonth() + 1);
                }
                entityNow.setDay(dateNow.getDate());
            } else {
                entityNow = DateTimeEntity.today();
            }
        } catch (Exception e) {
            e.printStackTrace();
            entityNow = DateTimeEntity.today();
        }
        if (wheelLayout != null) {
            wheelLayout.setRange(entityStart, entityEnd, entityNow); //日期范围
        }
    }

    @Override
    public void showAsDropDown(View anchor) {
        super.showAsDropDown(anchor);
        viewIcon.startAnimation(openAnim);
    }

    @Override
    public void dismiss() {
        super.dismiss();
        viewIcon.startAnimation(closeAnim);
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onClick(String startDate, String endDate);
    }
}
