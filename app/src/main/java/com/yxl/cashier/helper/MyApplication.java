package com.yxl.cashier.helper;

import com.tencent.bugly.crashreport.CrashReport;
import com.yxl.cashier.helper.utils.SystemTTS;
import com.yxl.commonlibrary.base.BaseApplication;

/**
 *
 */
public class MyApplication extends BaseApplication {
    public static SystemTTS systemTTS;
    public static int type,//当前处于页面 0.收银 1.退款 2.订单 3.汇总 4.设置 5.订单详情
            saleUniqueType,//订单号类型：0.订单号 1.商户号
            orderType;//订单类型：0.销售订单 1.退款订单
    public static String cashierMoney;//收银识别金额
    public static String saleListUnique;//订单编码
    public static boolean isCashierBase;//是否为设置中的收银设置
    public static String barCode;//小窗口扫码

    public static int x0, y0;//收银小窗口坐标

    @Override
    public void onCreate() {
        super.onCreate();
        setApplication(this);
        CrashReport.initCrashReport(getApplicationContext(), "ba212745a3", false);
        systemTTS = SystemTTS.getInstance(getApplicationContext());
    }

}