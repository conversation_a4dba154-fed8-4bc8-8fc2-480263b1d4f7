package com.yxl.cashier.helper;

import com.yxl.commonlibrary.http.Url;

/**
 * Describe:接口地址
 * Created by jingang on 2024/1/17
 */
public class ZURL {

//    public final static String DEBUG_URL = Url.baseUrl;//测试地址shopUpdate
//    private final static String RELEASE_URL = "https://platformt.allscm..net/gw/";//地址shopUpdate

    public final static String DEBUG_URL = "https://platformt.allscm.net/gw/";//测试地址
    private final static String RELEASE_URL = Url.baseUrl;//正式地址

    public static String URL = BuildConfig.DEBUG ? DEBUG_URL : RELEASE_URL;

    //登录
    public static String getLogin() {
        return URL + "cbdPluginPc/noAuth/shopLogin";
    }

    //扫码支付（收银）
    public static String getPay() {
        return URL + "cbdPluginPc/pay/scanPay";
    }

    //支付结果查询
    public static String getPayResult() {
        return URL + "cbdPluginPc/pay/queryPayResult";
    }

    //退款
    public static String getRefund() {
        return URL + "cbdPluginPc/pay/refund";
    }

    //撤销订单-取消付款（只针对被扫的处理中的订单, 只能在支付15s后调用）
    public static String getPayCancel() {
        return URL + "cbdPluginPc/pay/cancelPay";
    }

    //退款结果查询
    public static String getRefundResult() {
        return URL + "cbdPluginPc/pay/queryRefundResult";
    }

    //销售订单列表
    public static String getSaleList() {
        return URL + "cbdPluginPc/saleList/selectForPage";
    }

    //销售订单详情
    public static String getSaleInfo() {
        return URL + "cbdPluginPc/saleList/selectDetailBySaleListUnique";
    }

    //订单详情查询（通过商户单号）
    public static String getSaleInfoNo() {
        return URL + "cbdPluginPc/saleList/selectByOutTradeNo";
    }

    //退款订单列表
    public static String getRefundList() {
        return URL + "cbdPluginPc/returnList/selectForPage";
    }

    //查询日统计信息
    public static String getDailySummary() {
        return URL + "cbdPluginPc/dailySummary/queryDailySummary";
    }

    //查询商户配置
    public static String getSetting() {
        return URL + "cbdPluginPc/pluginSetting/selectPluginSetting";
    }

    //保存商户配置
    public static String getSettingSave() {
        return URL + "cbdPluginPc/pluginSetting/savePluginSetting";
    }

    //检查最新版本
    public static String getVersion() {
        return URL + "platformBaseWeb/noAuth/app/upgradeVersion?appCode=PC_PLUGIN_CBD&systemType=1";
    }

    //收款方式
    public static String getPaymentTypeList() {
        return URL + "cbdPluginPc/common/queryPaymentMethodEnums";
    }

    //金圈收银插件使用协议
    public static String getPrivacy() {
        return "https://yun.buyhoo.cc/purchase-app/html/agreement.html?id=10";
    }

}
