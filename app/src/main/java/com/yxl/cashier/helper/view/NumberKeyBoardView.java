package com.yxl.cashier.helper.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.TypedArray;
import android.os.Handler;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;

import com.yxl.cashier.helper.R;

/**
 * Describe:数字键盘（餐饮）
 * Created by jingang on 2023/5/27
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class NumberKeyBoardView extends LinearLayout {
    private String resultStr = "";
    private Context mContext;
    private OnMValueChangedListener onMValueChangedListener;

    public void setOnMValueChangedListener(OnMValueChangedListener onMValueChangedListener) {
        this.onMValueChangedListener = onMValueChangedListener;
    }

    public void setResultStr(String resultStr) {
        this.resultStr = resultStr;
    }

    public interface OnMValueChangedListener {
        void onChange(String var1);

        void onConfirm();
    }

    public NumberKeyBoardView(Context context) {
        this(context, null);
    }

    public NumberKeyBoardView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public NumberKeyBoardView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.mContext = context;
        TypedArray ta = context.getTheme().obtainStyledAttributes(attrs, R.styleable.CateringNumberKeyBoardView, defStyleAttr, 0);
//        confirm = ta.getInteger(R.styleable.CateringNumberKeyBoardView_confirm, 0);
        init();
    }

    private void init() {
        LayoutInflater.from(mContext).inflate(R.layout.layout_nkb1, this);
        new Handler().postDelayed(() -> {
            initListener();
        }, 10);
    }

    private void initListener() {
        class buttonsOnClickListener implements OnClickListener {
            @Override
            public void onClick(View v) {
                switch (v.getId()) {
                    case R.id.tv1:
                        addNumberToResult("1");
                        break;
                    case R.id.tv2:
                        addNumberToResult("2");
                        break;
                    case R.id.tv3:
                        addNumberToResult("3");
                        break;
                    case R.id.tv4:
                        addNumberToResult("4");
                        break;
                    case R.id.tv5:
                        addNumberToResult("5");
                        break;
                    case R.id.tv6:
                        addNumberToResult("6");
                        break;
                    case R.id.tv7:
                        addNumberToResult("7");
                        break;
                    case R.id.tv8:
                        addNumberToResult("8");
                        break;
                    case R.id.tv9:
                        addNumberToResult("9");
                        break;
                    case R.id.tv0:
                        addNumberToResult("0");
                        break;
                    case R.id.tvDrop:
                        addNumberToResult(".");
                        break;
                    case R.id.tvClear:
                        //清空
                        clearNumber();
                        break;
                    case R.id.linBack:
                        //删除
                        reduceNumberToResult();
                        break;
                    case R.id.tvConfirm:
                        //确认
                        if (onMValueChangedListener != null) {
                            onMValueChangedListener.onConfirm();
                        }
                        break;
                }
            }
        }
        findViewById(R.id.tv1).setOnClickListener(new buttonsOnClickListener());
        findViewById(R.id.tv2).setOnClickListener(new buttonsOnClickListener());
        findViewById(R.id.tv3).setOnClickListener(new buttonsOnClickListener());
        findViewById(R.id.tv4).setOnClickListener(new buttonsOnClickListener());
        findViewById(R.id.tv5).setOnClickListener(new buttonsOnClickListener());
        findViewById(R.id.tv6).setOnClickListener(new buttonsOnClickListener());
        findViewById(R.id.tv7).setOnClickListener(new buttonsOnClickListener());
        findViewById(R.id.tv8).setOnClickListener(new buttonsOnClickListener());
        findViewById(R.id.tv9).setOnClickListener(new buttonsOnClickListener());
        findViewById(R.id.tv0).setOnClickListener(new buttonsOnClickListener());
        findViewById(R.id.tvDrop).setOnClickListener(new buttonsOnClickListener());
        findViewById(R.id.tvClear).setOnClickListener(new buttonsOnClickListener());
        findViewById(R.id.linBack).setOnClickListener(new buttonsOnClickListener());
        findViewById(R.id.tvConfirm).setOnClickListener(new buttonsOnClickListener());
    }

    //清除数字
    public void clearNumber() {
        resultStr = "";
        if (onMValueChangedListener != null) {
            onMValueChangedListener.onChange(resultStr);
        }
    }

    //增加数字
    private void addNumberToResult(String numberStr) {
        if (!TextUtils.isEmpty(resultStr)) {
            if (resultStr.contains(".")) {
                if (".".equals(numberStr)) {
                    return;
                } else {
                    //限制小数点后面最多有两位小数
                    int strLength = resultStr.substring(resultStr.indexOf(".")).length();
                    if (strLength == 3) {
                        return;
                    }
                }
            }
        }

        //第一次输入的是点，显示0.
        if ("".equals(resultStr) && ".".equals(numberStr)) {
            resultStr = "0";
            //小程序中关系网录入姓名手机号，但收银机上人脸识别不显示addNumberToResult
        }

        //数字前多次输入0，只显示一个0
        if (resultStr != null && !"".equals(resultStr) && "0".equals(resultStr) && "0".equals(numberStr)) {
            return;
        }
        resultStr += numberStr;

        if (null != onMValueChangedListener) {
            if (resultStr.length() > 30) {
                resultStr = resultStr.substring(0, 30);
//                Toast.makeText(mContext, "金额不能大于9位", Toast.LENGTH_SHORT).show();
            }
            onMValueChangedListener.onChange(resultStr);
        }
    }

    //擦除数字
    private void reduceNumberToResult() {
        if (resultStr.length() > 0) {
            resultStr = resultStr.substring(0, resultStr.length() - 1);
        }
        if (onMValueChangedListener != null) {
            onMValueChangedListener.onChange(resultStr);
        }
    }

}
