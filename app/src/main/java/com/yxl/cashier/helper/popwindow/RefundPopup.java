package com.yxl.cashier.helper.popwindow;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.drawable.AnimationDrawable;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.yxl.cashier.helper.R;
import com.yxl.cashier.helper.utils.ViewUtils;
import com.yxl.cashier.helper.view.NumberKeyBoardView;
import com.yxl.commonlibrary.utils.DFUtils;

/**
 * Describe:popup（退款）
 * Created by jingang on 2023/5/30
 */
@SuppressLint("NonConstantResourceId")
public class RefundPopup extends PopupWindow {
    private static Context mContext;
    private static double money;
    private LinearLayout linToast;
    private ImageView ivToast, ivClose, ivCursor;
    private TextView tvToast, tvMoney, tvAll, etMoney, etHint;
    private NumberKeyBoardView keyBoardView;

    public RefundPopup(Context context) {
        super(context);
        View view = View.inflate(context, R.layout.popup_refund, null);
        setContentView(view);
        linToast = view.findViewById(R.id.linToast);
        ivToast = view.findViewById(R.id.ivToast);
        tvToast = view.findViewById(R.id.tvToast);
        ivClose = view.findViewById(R.id.ivPopupClose);
        tvMoney = view.findViewById(R.id.tvPopupMoney);
        tvAll = view.findViewById(R.id.tvPopupAll);
        etMoney = view.findViewById(R.id.etPopupMoney);
        etHint = view.findViewById(R.id.etPopupHint);
        ivCursor = view.findViewById(R.id.ivCursor);
        keyBoardView = view.findViewById(R.id.numberKeyBoardView);

        linToast.setAlpha(0f);
        ((AnimationDrawable) ivCursor.getDrawable()).start();
        tvAll.setOnClickListener(v -> {
            etHint.setVisibility(View.GONE);
            etMoney.setText(DFUtils.getNum4(money));
            keyBoardView.setResultStr(DFUtils.getNum4(money));
        });
        ivClose.setOnClickListener(v -> dismiss());
        tvMoney.setText(DFUtils.getNum2(money));
        keyBoardView.setOnMValueChangedListener(new NumberKeyBoardView.OnMValueChangedListener() {
            @Override
            public void onChange(String var1) {
                etMoney.setText(var1);
                if (TextUtils.isEmpty(var1)) {
                    etHint.setVisibility(View.VISIBLE);
                } else {
                    etHint.setVisibility(View.GONE);
                }
                if (!TextUtils.isEmpty(etMoney.getText().toString().trim())) {
                    if (Double.parseDouble(etMoney.getText().toString().trim()) > money) {
                        etMoney.setText(DFUtils.getNum4(money));
                        keyBoardView.setResultStr(DFUtils.getNum4(money));
                    }
                }
            }

            @Override
            public void onConfirm() {
                if (TextUtils.isEmpty(etMoney.getText().toString().trim())) {
                    showToast(1, "请输入退款金额");
                    return;
                }
                if (Double.parseDouble(etMoney.getText().toString().trim()) == 0) {
                    showToast(1, "请输入退款金额");
                    return;
                }
                if (listener != null) {
                    listener.onRefund(Double.parseDouble(etMoney.getText().toString().trim()));
                    dismiss();
                }
            }
        });
    }

    public static void showDialog(Context context, int width, int height, View viewShow, double money, MyListener listener) {
        RefundPopup.mContext = context;
        RefundPopup.money = money;

        RefundPopup popupWindow = new RefundPopup(context);
        setListener(listener);
        popupWindow.setWidth(width + 2);
        popupWindow.setHeight(height + 4);
        //设置可以获取焦点
        popupWindow.setFocusable(false);
        //设置可以触摸弹出框以外的区域
        popupWindow.setOutsideTouchable(true);
        //放在具体控件下方
        popupWindow.showAsDropDown(viewShow);
    }

    /**
     * 吐司
     *
     * @param type 0.正确 1.错误
     * @param msg
     */
    private void showToast(int type, String msg) {
        if (type == 0) {
            linToast.setBackgroundResource(R.drawable.shape_green_5);
            ivToast.setImageResource(R.mipmap.ic_toast001);
        } else {
            linToast.setBackgroundResource(R.drawable.shape_red_5);
            ivToast.setImageResource(R.mipmap.ic_toast002);
        }
        tvToast.setText(msg);
        ViewUtils.fadeInAndFadeOut(linToast);
    }

    private static MyListener listener;

    public static void setListener(MyListener listener) {
        RefundPopup.listener = listener;
    }

    public interface MyListener {
        void onRefund(double money);
    }
}
