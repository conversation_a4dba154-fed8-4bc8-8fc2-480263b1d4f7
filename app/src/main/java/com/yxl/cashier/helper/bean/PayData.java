package com.yxl.cashier.helper.bean;

import java.io.Serializable;

/**
 * Describe:收银（实体类）
 * Created by jingang on 2024/1/18
 */
public class PayData implements Serializable {

    /**
     * payStatus : SUCCESS
     * saleListUnique : 170554358236698
     * totalAmount : 0.02
     * actualAmount : 0.02
     */

    private String payStatus;//INIT-未支付,DOING-支付中，SUCCESS-支付成功，FAIL-支付失败，CLOSE-订单已关闭，CANCEL-订单已取         支付结果
    private String saleListUnique;
    //    private String totalAmount;//应付金额
//    private String actualAmount;//实收金额
//    private String discountAmount;//优惠金额
//    private String couponAmount;//优惠券金额
    private double totalAmount;//应付金额
    private double actualAmount;//实收金额
    private double discountAmount;//优惠金额
    private double couponAmount;//优惠券金额
    private String msg;//支付提示

    public String getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(String payStatus) {
        this.payStatus = payStatus;
    }

    public String getSaleListUnique() {
        return saleListUnique;
    }

    public void setSaleListUnique(String saleListUnique) {
        this.saleListUnique = saleListUnique;
    }

//    public String getTotalAmount() {
//        return totalAmount;
//    }
//
//    public void setTotalAmount(String totalAmount) {
//        this.totalAmount = totalAmount;
//    }
//
//    public String getActualAmount() {
//        return actualAmount;
//    }
//
//    public void setActualAmount(String actualAmount) {
//        this.actualAmount = actualAmount;
//    }
//
//    public String getDiscountAmount() {
//        return discountAmount;
//    }
//
//    public void setDiscountAmount(String discountAmount) {
//        this.discountAmount = discountAmount;
//    }
//
//    public String getCouponAmount() {
//        return couponAmount;
//    }
//
//    public void setCouponAmount(String couponAmount) {
//        this.couponAmount = couponAmount;
//    }


    public double getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(double totalAmount) {
        this.totalAmount = totalAmount;
    }

    public double getActualAmount() {
        return actualAmount;
    }

    public void setActualAmount(double actualAmount) {
        this.actualAmount = actualAmount;
    }

    public double getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(double discountAmount) {
        this.discountAmount = discountAmount;
    }

    public double getCouponAmount() {
        return couponAmount;
    }

    public void setCouponAmount(double couponAmount) {
        this.couponAmount = couponAmount;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
