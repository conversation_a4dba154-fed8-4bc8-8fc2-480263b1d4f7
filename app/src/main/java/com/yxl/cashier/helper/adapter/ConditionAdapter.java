package com.yxl.cashier.helper.adapter;

import android.content.Context;
import android.widget.TextView;

import com.yxl.cashier.helper.Interface.OnItemClickListener;
import com.yxl.cashier.helper.R;
import com.yxl.cashier.helper.bean.ConditionData;
import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;

/**
 * Describe:条件列表（适配器）
 * Created by jingang on 2023/12/29
 */
public class ConditionAdapter extends BaseAdapter<ConditionData> {
    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public ConditionAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_condition;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        if (onItemClickListener != null) {
            holder.itemView.setOnClickListener(v -> onItemClickListener.onItemClick(v, position));
        }
        TextView tvName = holder.getView(R.id.tvItemName);
        tvName.setText(mDataList.get(position).getName());
    }
}
