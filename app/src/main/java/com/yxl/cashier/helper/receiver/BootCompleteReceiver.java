package com.yxl.cashier.helper.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.yxl.cashier.helper.LauncherActivity;
import com.yxl.commonlibrary.base.BaseApplication;

/**
 * Describe:创建广播，开机代码
 * Created by jingang on 2024/1/9
 */
public class BootCompleteReceiver extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {
        //This method is called when the BroadcastReceiver is receiving
        Log.e("BootCompleteReceiver", "接收到广播-开机启动");
        if (Intent.ACTION_BOOT_COMPLETED.equals(intent.getAction())) {
            // 在这里执行开机启动时需要执行的任务
            // 如启动服务、初始化数据等
            if (BaseApplication.getInstance().isStartUp()) {
                context.startActivity(new Intent(context, LauncherActivity.class));
            }
        }
    }
}