package com.yxl.cashier.helper.bean;

import java.io.Serializable;

/**
 * Describe:退款（实体类）
 * Created by jingang on 2024/1/23
 */
public class RefundData implements Serializable {
    /**
     * saleListUnique : 170597734996511
     * retListUnique : 291705999593557
     * refundStatus : SUCCESS
     * refundAmount : 0.02
     * actualAmount : 0.02
     * msg : null
     */

    private String saleListUnique;//订单编码
    private String retListUnique;//商户编码
    private String refundStatus;//退款状态 SUCCESS-成功，FAIL-失败，DOING-处理中
//    private String refundAmount;//应退金额
//    private String actualAmount;//实退金额
    private double refundAmount;//应退金额
    private double actualAmount;//实退金额
    private String msg;//描述

    public String getSaleListUnique() {
        return saleListUnique;
    }

    public void setSaleListUnique(String saleListUnique) {
        this.saleListUnique = saleListUnique;
    }

    public String getRetListUnique() {
        return retListUnique;
    }

    public void setRetListUnique(String retListUnique) {
        this.retListUnique = retListUnique;
    }

    public String getRefundStatus() {
        return refundStatus;
    }

    public void setRefundStatus(String refundStatus) {
        this.refundStatus = refundStatus;
    }

//    public String getRefundAmount() {
//        return refundAmount;
//    }
//
//    public void setRefundAmount(String refundAmount) {
//        this.refundAmount = refundAmount;
//    }
//
//    public String getActualAmount() {
//        return actualAmount;
//    }
//
//    public void setActualAmount(String actualAmount) {
//        this.actualAmount = actualAmount;
//    }


    public double getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(double refundAmount) {
        this.refundAmount = refundAmount;
    }

    public double getActualAmount() {
        return actualAmount;
    }

    public void setActualAmount(double actualAmount) {
        this.actualAmount = actualAmount;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
