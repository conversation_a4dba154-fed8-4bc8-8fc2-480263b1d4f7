package com.yxl.cashier.helper.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.cashier.helper.Interface.OnItemClickListener;
import com.yxl.cashier.helper.MyApplication;
import com.yxl.cashier.helper.R;
import com.yxl.cashier.helper.bean.OrderListData;
import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;
import com.yxl.commonlibrary.utils.DFUtils;

/**
 * Describe:订单列表（适配器）
 * Created by jingang on 2023/12/27
 */
public class OrderAdapter extends BaseAdapter<OrderListData.RowsBean> {
    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public OrderAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_order;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        if (onItemClickListener != null) {
            holder.itemView.setOnClickListener(v -> onItemClickListener.onItemClick(v, position));
        }
        LinearLayout lin, linOrder, linRefund;
        TextView tvNo, tvTime, tvTotal, tvRealTotal, tvStatus,
                tvRefundNo, tvRefundTime, tvRefundTotal, tvStaffName, tvRefundStatus;

        lin = holder.getView(R.id.linItem);

        linOrder = holder.getView(R.id.linItemOrder);
        tvNo = holder.getView(R.id.tvItemNo);
        ImageView ivRefund = holder.getView(R.id.ivItemRefund);
        tvTime = holder.getView(R.id.tvItemTime);
        tvTotal = holder.getView(R.id.tvItemTotal);
        tvRealTotal = holder.getView(R.id.tvItemRealTotal);
        tvStatus = holder.getView(R.id.tvItemStatus);

        linRefund = holder.getView(R.id.linItemRefund);
        tvRefundNo = holder.getView(R.id.tvItemRefundNo);
        tvRefundTime = holder.getView(R.id.tvItemRefundTime);
        tvRefundTotal = holder.getView(R.id.tvItemRefundTotal);
        tvStaffName = holder.getView(R.id.tvItemStaffName);
        tvRefundStatus = holder.getView(R.id.tvItemRefundStatus);

        if (position % 2 == 0) {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.white));
        } else {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.color_f7));
        }
        if (MyApplication.orderType == 0) {
            linOrder.setVisibility(View.VISIBLE);
            linRefund.setVisibility(View.GONE);
            tvNo.setText(mDataList.get(position).getSaleListUnique());

            //退款金额>0显示退款标记
            double refundMoney;
            try {
                refundMoney = mDataList.get(position).getRetListTotalMoney();
            } catch (Exception e) {
                refundMoney = 0;
            }
            if (refundMoney > 0) {
                ivRefund.setVisibility(View.VISIBLE);
            } else {
                ivRefund.setVisibility(View.INVISIBLE);
            }

            tvTime.setText(mDataList.get(position).getSaleListDatetime());
            tvTotal.setText("￥" + DFUtils.getNum2(mDataList.get(position).getSaleListTotal()));
            tvRealTotal.setText("￥" + DFUtils.getNum2(mDataList.get(position).getSaleListActuallyReceived()));
            //付款状态 2-未付款,3-已完成
            if (TextUtils.isEmpty(mDataList.get(position).getPayStatus())) {
                tvStatus.setText("");
            } else {
//                switch (mDataList.get(position).getSaleListState()) {
//                    case 2:
//                        tvStatus.setText("未付款");
//                        break;
//                    case 3:
//                        tvStatus.setText("已完成");
//                        break;
//                    default:
//                        tvStatus.setText("");
//                        break;
//                }
                //支付状态：CANCEL-已取消，DOING-未付款，SUCCESS-已支付，CLOSE-已关闭
                switch (mDataList.get(position).getPayStatus()) {
                    case "CANCEL":
                        tvStatus.setText("已取消");
                        break;
                    case "DOING":
                        tvStatus.setText("未付款");
                        break;
                    case "SUCCESS":
                        tvStatus.setText("已完成");
                        break;
                    case "FAIL":
                        tvStatus.setText("支付失败");
                        break;
                    default:
                        tvStatus.setText("");
                        break;
                }
            }
        } else {
            linOrder.setVisibility(View.GONE);
            linRefund.setVisibility(View.VISIBLE);
            tvRefundNo.setText(mDataList.get(position).getRetListUnique());
            tvRefundTime.setText(mDataList.get(position).getRetListDatetime());
            tvRefundTotal.setText("￥" + DFUtils.getNum2(mDataList.get(position).getRetListTotalMoney()));
            tvStaffName.setText(mDataList.get(position).getStaffName());
            tvRefundStatus.setText(mDataList.get(position).getRetListStateName());
        }
    }
}
