package com.yxl.cashier.helper.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.widget.TextView;

import com.yxl.cashier.helper.R;
import com.yxl.cashier.helper.bean.OrderInfoData;
import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;
import com.yxl.commonlibrary.utils.DFUtils;

/**
 * Describe:优惠金额弹窗（适配器）
 * Created by jingang on 2024/3/29
 */
public class DiscountAdapter extends BaseAdapter<OrderInfoData.DiscountListBean> {

    public DiscountAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_discount;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvName = holder.getView(R.id.tvItemName);
        tvName.setText(mDataList.get(position).getName() + ":减" + DFUtils.getNum2(mDataList.get(position).getMoney()));
    }
}
