package com.yxl.cashier.helper.dialog;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.util.Log;
import android.view.inputmethod.InputMethodManager;

import androidx.annotation.NonNull;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleObserver;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.LifecycleRegistry;

import com.yxl.commonlibrary.utils.ToastUtils;

/**
 * Describe:
 * Created by jingang on 2023/5/31
 */
public class BaseDialog extends Dialog implements LifecycleObserver, LifecycleOwner {
    protected LifecycleRegistry lifecycleRegistry;
    public String tag = "BaseDialog";
    private long currentTime;

    public BaseDialog(@NonNull Context context, int style) {
        super(context, style);
        lifecycleRegistry = new LifecycleRegistry(this);
    }

    public BaseDialog(Context context, int theme, int res) {
        super(context, theme);
        setContentView(res);
        setCanceledOnTouchOutside(false);
    }

    @NonNull
    @Override
    public Lifecycle getLifecycle() {
        return lifecycleRegistry;
    }

    /**
     * toast提示
     *
     * @param message
     */
    public void showMessage(String message) {
        ToastUtils.getInstance(getContext()).showMessage(message);
    }

    /**
     * 隐藏软键盘
     *
     * @param activity 当前Activity
     */
    public void hideSoftInput(Activity activity) {
        InputMethodManager inputMethodManager =
                (InputMethodManager) activity.getSystemService(Context.INPUT_METHOD_SERVICE);
        inputMethodManager.hideSoftInputFromWindow(
                activity.getWindow().getDecorView().getWindowToken(), 0);
    }

    /**
     * 防止快速点击引起ThreadPoolExecutor频繁创建销毁引起crash
     *
     * @return
     */
    public boolean isQuicklyClick() {
        boolean result = false;
        if (System.currentTimeMillis() - currentTime <= 500) {
            result = true;
            Log.e("111111", "is too quickly!");
        }
        currentTime = System.currentTimeMillis();
        return result;
    }

}
