package com.yxl.cashier.helper.popwindow;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.CountDownTimer;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.yxl.cashier.helper.R;
import com.yxl.cashier.helper.bean.RefundData;
import com.yxl.commonlibrary.Constants;
import com.yxl.commonlibrary.base.BaseApplication;
import com.yxl.commonlibrary.utils.DFUtils;
import com.yxl.commonlibrary.utils.DensityUtils;

/**
 * Describe:popup（退款结果）
 * Created by jingang on 2023/5/30
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n", "StaticFieldLeak"})
public class RefundResultPopup extends PopupWindow implements View.OnClickListener {
    private static Context mContext;
    private ImageView ivClose, ivStatus;
    private TextView tvStatus, tvMoney, tvTips, tvDownTimer, tvBack, tvAgain;
    private static RefundData data;
    private static String msg;

    public RefundResultPopup(Context context) {
        super(context);
        View view = View.inflate(context, R.layout.popup_refund_result, null);
        setContentView(view);
        ivClose = view.findViewById(R.id.ivPopupClose);
        ivStatus = view.findViewById(R.id.ivPopupStatus);
        tvStatus = view.findViewById(R.id.tvPopupStatus);
        tvMoney = view.findViewById(R.id.tvPopupMoney);
        tvTips = view.findViewById(R.id.tvPopupTips);
        tvDownTimer = view.findViewById(R.id.tvPopupDownTimer);
        tvBack = view.findViewById(R.id.tvPopupBack);
        tvAgain = view.findViewById(R.id.tvPopupAgain);

        ivClose.setOnClickListener(this);
        tvBack.setOnClickListener(this);
        tvAgain.setOnClickListener(this);
        setUI();
    }

    public static void showDialog(Context context, int width, int height, View viewShow, RefundData data, String msg, MyListener listener) {
        RefundResultPopup.mContext = context;
        RefundResultPopup.data = data;
        RefundResultPopup.msg = msg;

        RefundResultPopup popupWindow = new RefundResultPopup(context);
        setListener(listener);
        popupWindow.setWidth(width + 2);
        popupWindow.setHeight(height + 4);
        //设置可以获取焦点
        popupWindow.setFocusable(false);
        //设置可以触摸弹出框以外的区域
        popupWindow.setOutsideTouchable(true);
        //放在具体控件下方
        popupWindow.showAsDropDown(viewShow);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivPopupClose:
            case R.id.tvPopupBack:
                //返回
                dismiss();
                break;
            case R.id.tvPopupAgain:
                //重新退款
                if (listener != null) {
                    listener.onRefund();
                    dismiss();
                }
                break;
        }
    }

    @Override
    public void dismiss() {
        super.dismiss();
        cancelTimer();
    }

    /**
     * 更新UI
     */
    private void setUI() {
        if (data == null) {
            setUIFail();
            return;
        }
        //退款状态 SUCCESS-成功，FAIL-失败，DOING-处理中
        if (TextUtils.isEmpty(data.getRefundStatus())) {
            setUIFail();
            return;
        }
        switch (data.getRefundStatus()) {
            case "SUCCESS":
                ivStatus.setImageResource(R.mipmap.ic_status001);
                tvStatus.setText("退款成功");
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.green));
                tvMoney.setVisibility(View.VISIBLE);
                tvMoney.setText("退款金额：" + DFUtils.getNum2(data.getActualAmount()));
                if (BaseApplication.getInstance().isRefundFinish()) {
                    tvDownTimer.setVisibility(View.VISIBLE);
                    time();
                } else {
                    tvDownTimer.setVisibility(View.GONE);
                }
                tvTips.setVisibility(View.GONE);
                tvAgain.setVisibility(View.GONE);
                break;
            case "DOING":
                ivStatus.setImageResource(R.drawable.cashiering);
                tvStatus.setText("退款中");
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.color_666));
                tvMoney.setVisibility(View.VISIBLE);
                tvMoney.setText("退款金额：" + DFUtils.getNum2(data.getActualAmount()));
                tvDownTimer.setVisibility(View.GONE);
                tvTips.setVisibility(View.GONE);
                tvAgain.setVisibility(View.VISIBLE);
                break;
            default:
                setUIFail();
                break;
        }
    }

    /**
     * 退款失败
     */
    private void setUIFail() {
        ivStatus.setImageResource(R.mipmap.ic_status002);
        tvStatus.setText("退款失败");
        tvStatus.setTextColor(mContext.getResources().getColor(R.color.red));
        tvMoney.setVisibility(View.GONE);
        tvTips.setVisibility(View.VISIBLE);
        tvDownTimer.setVisibility(View.GONE);
        tvTips.setText("失败原因：\n" + msg);
        tvAgain.setVisibility(View.VISIBLE);
    }

    /**************************倒计时start**************************/
    //倒计时
    private CountDownTimer countdown;

    /**
     * 倒计时（弹出退出登录弹窗）
     */
    public void time() {
        cancelTimer();
        if (countdown == null) {
            countdown = new CountDownTimer(Constants.finish_time * 1000L, 1000L) {
                @SuppressLint("SetTextI18n")
                @Override
                public void onTick(long millisUntilFinished) {
                    if (tvDownTimer != null) {
                        tvDownTimer.setText((millisUntilFinished / 1000) + "秒后关闭窗口");
                    }
                }

                @Override
                public void onFinish() {
                    //倒计时结束时触发
                    if (listener != null) {
                        listener.onClose();
                        dismiss();
                    }
                }
            };
            countdown.start();
        } else {
            countdown.start();
        }
    }

    /**
     * 计时器清除（没暂停）
     */
    public void cancelTimer() {
        if (countdown != null) {
            countdown.cancel();
        }
    }

    private static MyListener listener;

    public static void setListener(MyListener listener) {
        RefundResultPopup.listener = listener;
    }

    public interface MyListener {
        void onRefund();

        void onClose();
    }
}
