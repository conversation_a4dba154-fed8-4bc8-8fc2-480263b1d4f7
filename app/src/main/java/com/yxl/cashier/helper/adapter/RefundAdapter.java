package com.yxl.cashier.helper.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.cashier.helper.Interface.OnItemClickListener;
import com.yxl.cashier.helper.R;
import com.yxl.cashier.helper.bean.OrderInfoData;
import com.yxl.commonlibrary.base.BaseAdapter;
import com.yxl.commonlibrary.base.ViewHolder;
import com.yxl.commonlibrary.utils.DFUtils;

/**
 * Describe:退款信息（适配器）
 * Created by jingang on 2023/12/28
 */
public class RefundAdapter extends BaseAdapter<OrderInfoData.ReturnListBean> {
    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public RefundAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_refund;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        if (onItemClickListener != null) {
            holder.itemView.setOnClickListener(v -> onItemClickListener.onItemClick(v, position));
        }
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvNo, tvTime, tvTotal, tvStaffName, tvStatus;
        tvNo = holder.getView(R.id.tvItemNo);
        tvTime = holder.getView(R.id.tvItemTime);
        tvTotal = holder.getView(R.id.tvItemTotal);
        tvStaffName = holder.getView(R.id.tvItemStaffName);
        tvStatus = holder.getView(R.id.tvItemStatus);

        if (position % 2 == 0) {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.white));
        } else {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.color_f7));
        }
        tvNo.setText(String.valueOf(mDataList.get(position).getRetListUnique()));
        tvTime.setText(mDataList.get(position).getRetListDatetime());
        tvTotal.setText("￥" + DFUtils.getNum2(mDataList.get(position).getRetListTotalMoney()));
        tvStaffName.setText(mDataList.get(position).getStaffName());
        //退款状态:1-未退款，2-已退款
        if (TextUtils.isEmpty(mDataList.get(position).getRetListState())) {
            tvStatus.setText("");
        } else {
            switch (mDataList.get(position).getRetListState()) {
                case "1":
                    tvStatus.setText("未退款");
                    break;
                case "2":
                    tvStatus.setText("已完成");
                    break;
                default:
                    tvStatus.setText("");
                    break;
            }
        }
    }
}
