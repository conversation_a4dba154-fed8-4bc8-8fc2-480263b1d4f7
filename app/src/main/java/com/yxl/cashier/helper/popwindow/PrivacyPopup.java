package com.yxl.cashier.helper.popwindow;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.net.http.SslError;
import android.os.AsyncTask;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.webkit.SslErrorHandler;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ImageView;
import android.widget.PopupWindow;

import com.yxl.cashier.helper.R;

import java.net.URLDecoder;

/**
 * Describe:popup（退款）
 * Created by jingang on 2023/5/30
 */
@SuppressLint("NonConstantResourceId")
public class PrivacyPopup extends PopupWindow {
    private static Context mContext;
    private ImageView ivBack, ivClose;
    private WebView webView;

    private static String url;

    public PrivacyPopup(Context context) {
        super(context);
        View view = View.inflate(context, R.layout.pop_privacy, null);
        setContentView(view);
        ivBack = view.findViewById(R.id.ivPopupBack);
        ivClose = view.findViewById(R.id.ivPopupClose);
        webView = view.findViewById(R.id.webView);
        new WebViewTask().execute();
        ivBack.setOnClickListener(v -> dismiss());
        ivClose.setOnClickListener(v -> {
            if (listener != null) {
                listener.onCloseClick();
            }
        });
    }

    public static void showDialog(Context context, String url, int width, int height, View viewShow, MyListener listener) {
        PrivacyPopup.mContext = context;
        PrivacyPopup.url = url;
        PrivacyPopup.listener = listener;
        PrivacyPopup popupWindow = new PrivacyPopup(context);
//        popupWindow.setWidth(width + 2);
//        popupWindow.setHeight(height + 4);
        popupWindow.setWidth(width);
        popupWindow.setHeight(height);
        // 设置PopupWindow的背景透明
        popupWindow.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        //设置可以获取焦点
        popupWindow.setFocusable(false);
        //设置可以触摸弹出框以外的区域
        popupWindow.setOutsideTouchable(false);
        //放在具体控件下方
        popupWindow.showAsDropDown(viewShow);
    }

    private class MyWebViewClient extends WebViewClient {

        @Override
        public void onReceivedSslError(WebView view, SslErrorHandler handler, SslError error) {
            handler.proceed();
            super.onReceivedSslError(view, handler, error);
            Log.d("证书错误", "onReceivedSslError: "); //如果是证书问题，会打印出此条log到console
        }

        @Override
        public boolean shouldOverrideUrlLoading(WebView view, String url) {
            Log.e("MessageView", url); // tel:13330983586
            String overUrl = URLDecoder.decode(url);// url.URLDecode;
            Log.e("urlcode", overUrl);
            if ((url != null && !url.equals("")) && overUrl.contains("tag=backapp")) {
                dismiss();
                return true;
            } else {
                return super.shouldOverrideUrlLoading(view, url);
            }
        }

        @Override
        public void onPageFinished(WebView view, String url) {
            super.onPageFinished(view, url);

        }

        @Override
        public void onPageStarted(WebView view, String url, Bitmap favicon) {
            super.onPageStarted(view, url, favicon);
        }

        @Override
        public void onReceivedError(WebView view, int errorCode,
                                    String description, String failingUrl) {
            super.onReceivedError(view, errorCode, description, failingUrl);
        }
    }

    private class WebViewTask extends AsyncTask<Void, Void, Boolean> {

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
        }

        protected Boolean doInBackground(Void... param) {
            return false;
        }

        @Override
        protected void onPostExecute(Boolean result) {
            WebSettings webSettings = webView.getSettings();
            webSettings.setDefaultTextEncodingName("UTF-8");
            webSettings.setJavaScriptEnabled(true);
            webSettings.setBuiltInZoomControls(true);
            webSettings.setDisplayZoomControls(false);//隐藏webview缩放比例
            webSettings.setLayoutAlgorithm(WebSettings.LayoutAlgorithm.SINGLE_COLUMN);
            webSettings.setUseWideViewPort(true);//适配手机
            webSettings.setLoadWithOverviewMode(true);//适配手机
            webView.setWebViewClient(new MyWebViewClient());
            if (TextUtils.isEmpty(url)) {
                return;
            }
            webView.loadUrl(url);
        }
    }

    private static MyListener listener;

    public interface MyListener {
        void onCloseClick();
    }
}
