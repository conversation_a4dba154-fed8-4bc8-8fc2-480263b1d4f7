<resources xmlns:tools="http://schemas.android.com/tools" xmlns:android="http://schemas.android.com/apk/res/android">

    <style name="AppBaseTheme" parent="android:Theme.Light" />

    <!-- Application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/blue</item>
        <item name="colorPrimaryDark">@color/blue</item>
        <item name="colorAccent">@color/blue</item>

        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="background">@color/transparent</item>
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Translucent</item>

        <!-- API 19及以上 -->
        <item name="android:windowFullscreen">true</item>
        <!-- API 14及以上 -->
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowTranslucentNavigation">true</item>

        <!-- 对于Android 21及以上，特别是带有手势导航的设备 -->
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>

        <!-- 对于Android 11及以上，可以尝试利用持久化设置隐藏状态栏和导航栏 -->
        <item name="android:windowLightStatusBar" tools:ignore="NewApi">false</item>
        <!-- API 28及以上 -->
        <item name="android:enforceNavigationBarContrast" tools:ignore="NewApi">false</item>
    </style>

    <!--popupwindow样式-->
    <style name="style_pop" parent="@android:style/Theme.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
    </style>

    <!--默认dialog样式-->
    <style name="dialog_style" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:backgroundDimEnabled">true</item>

        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <!-- 针对Android 4.4及以上版本，需要额外设置透明状态栏 -->
        <item name="android:windowTranslucentStatus">true</item>
        <!-- 针对Android 5.0及以上版本，还需要设置透明导航栏 -->
        <item name="android:windowTranslucentNavigation">true</item>
        <!-- 或者针对Android 8.0（API 26）以上系统使用以下属性来完全隐藏导航栏 -->
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
    </style>

</resources>