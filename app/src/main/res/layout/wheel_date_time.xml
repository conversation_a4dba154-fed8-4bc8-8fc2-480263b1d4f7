<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <com.github.gzuliyujiang.wheelview.widget.NumberWheelView
        android:id="@+id/wheelYear"
        style="@style/WheelDefault"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:visibility="visible" />

    <com.github.gzuliyujiang.wheelview.widget.NumberWheelView
        android:id="@+id/wheelMonth"
        style="@style/WheelDefault"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:visibility="visible" />

    <com.github.gzuliyujiang.wheelview.widget.NumberWheelView
        android:id="@+id/wheelDay"
        style="@style/WheelDefault"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:visibility="visible" />

</LinearLayout>
