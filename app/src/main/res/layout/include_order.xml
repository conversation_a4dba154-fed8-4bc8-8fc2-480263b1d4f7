<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/linOrder"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent"
    android:orientation="vertical"
    android:visibility="gone">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_white_top_right_5"
        android:gravity="center"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvOrderSale"
            style="@style/text_16_333"
            android:layout_gravity="bottom"
            android:layout_marginStart="@dimen/dp10"
            android:background="@drawable/shape_f7_top_5"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp6"
            android:text="销售订单"
            android:textColor="@color/green"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvOrderRefund"
            style="@style/text_16_333"
            android:layout_gravity="bottom"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp6"
            android:text="退款订单"
            android:textStyle="bold" />

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="0.8" />

        <EditText
            android:id="@+id/etOrderSearch"
            style="@style/text_12_333"
            android:layout_width="0dp"
            android:layout_marginVertical="@dimen/dp8"
            android:layout_weight="1"
            android:background="@drawable/shape_e1e2e3_kuang_5"
            android:drawableLeft="@mipmap/ic_search001"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:hint="商户单号/订单号"
            android:inputType="text"
            android:maxLines="1"
            android:paddingHorizontal="@dimen/dp6"
            android:paddingVertical="@dimen/dp4" />

        <ImageView
            android:id="@+id/ivOrderClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp10"
            android:src="@mipmap/ic_close001" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal"
        android:padding="@dimen/dp10">

        <LinearLayout
            android:id="@+id/linOrderType"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp10"
            android:layout_weight="1"
            android:background="@drawable/shape_e1e2e3_kuang_5"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="@dimen/dp5">

            <TextView
                android:id="@+id/tvOrderType"
                style="@style/text_12_333"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="收款方式"
                tools:ignore="NestedWeights" />

            <ImageView
                android:id="@+id/ivOrderType"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@mipmap/ic_arrow001" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/linOrderStatus"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp10"
            android:layout_weight="1"
            android:background="@drawable/shape_e1e2e3_kuang_5"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="@dimen/dp5">

            <TextView
                android:id="@+id/tvOrderStatus"
                style="@style/text_12_333"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="订单状态"
                tools:ignore="NestedWeights" />

            <ImageView
                android:id="@+id/ivOrderStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@mipmap/ic_arrow001" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/linOrderStartTime"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/shape_e1e2e3_kuang_5"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="@dimen/dp5">

            <TextView
                android:id="@+id/tvOrderStartTime"
                style="@style/text_12_333"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:ellipsize="end"
                android:hint="开始时间"
                android:maxLines="1"
                tools:ignore="NestedWeights" />

            <ImageView
                android:id="@+id/ivOrderStartTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@mipmap/ic_arrow001" />

        </LinearLayout>

        <TextView
            style="@style/text_14_999"
            android:layout_margin="@dimen/dp5"
            android:text="~" />

        <LinearLayout
            android:id="@+id/linOrderEndTime"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp10"
            android:layout_weight="1"
            android:background="@drawable/shape_e1e2e3_kuang_5"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="@dimen/dp5">

            <TextView
                android:id="@+id/tvOrderEndTime"
                style="@style/text_12_333"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:ellipsize="end"
                android:hint="结束时间"
                android:maxLines="1"
                tools:ignore="NestedWeights" />

            <ImageView
                android:id="@+id/ivOrderEndTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@mipmap/ic_arrow001" />

        </LinearLayout>

        <TextView
            android:id="@+id/tvOrderResetting"
            style="@style/text_12_333"
            android:background="@drawable/shape_orange_kuang_5"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp5"
            android:text="重置"
            android:textColor="@color/orange" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/linOrderTop"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp10"
        android:background="@drawable/shape_e7eae7_top_5"
        android:gravity="center"
        android:orientation="horizontal">

        <TextView
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_weight="2"
            android:gravity="center"
            android:paddingVertical="@dimen/dp6"
            android:text="订单号" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@mipmap/ic_refund001"
            android:visibility="invisible" />

        <TextView
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_weight="2"
            android:gravity="center"
            android:paddingVertical="@dimen/dp6"
            android:text="订单时间" />

        <TextView
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:paddingVertical="@dimen/dp6"
            android:text="订单金额" />

        <TextView
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:paddingVertical="@dimen/dp6"
            android:text="实收金额" />

        <TextView
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:paddingVertical="@dimen/dp6"
            android:text="订单状态" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/linRefundTop"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp10"
        android:background="@drawable/shape_e7eae7_top_5"
        android:gravity="center"
        android:orientation="horizontal"
        android:visibility="gone">

        <TextView
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_weight="2"
            android:gravity="center"
            android:paddingVertical="@dimen/dp6"
            android:text="退款单号" />

        <TextView
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_weight="2"
            android:gravity="center"
            android:paddingVertical="@dimen/dp6"
            android:text="退款时间" />

        <TextView
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:paddingVertical="@dimen/dp6"
            android:text="退款金额" />

        <TextView
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:paddingVertical="@dimen/dp6"
            android:text="操作人" />

        <TextView
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:paddingVertical="@dimen/dp6"
            android:text="退款状态" />

    </LinearLayout>

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/srlOrder"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginHorizontal="@dimen/dp10"
        android:background="@color/transparent">

        <com.scwang.smart.refresh.header.ClassicsHeader
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/transparent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvOrder"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/transparent"
                android:overScrollMode="never" />

            <LinearLayout
                android:id="@+id/linOrderEmpty"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingTop="@dimen/dp40"
                android:visibility="gone">

                <ImageView
                    android:id="@+id/ivOrderEmpty"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@mipmap/ic_empty" />

                <TextView
                    android:id="@+id/tvOrderEmpty"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp26"
                    android:textColor="@color/color_666"
                    android:textSize="@dimen/f14"
                    android:textStyle="bold" />

            </LinearLayout>

        </RelativeLayout>

        <com.scwang.smart.refresh.footer.ClassicsFooter
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

</LinearLayout>