<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/linItem"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="horizontal"
    android:paddingVertical="@dimen/dp5">

    <TextView
        android:id="@+id/tvItemNo"
        style="@style/text_12_333"
        android:layout_width="0dp"
        android:layout_weight="1.5"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:text="" />

    <TextView
        android:id="@+id/tvItemTime"
        style="@style/text_12_333"
        android:layout_width="0dp"
        android:layout_weight="1.5"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:text="" />

    <TextView
        android:id="@+id/tvItemTotal"
        style="@style/text_12_333"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:text="￥0.00" />

    <TextView
        android:id="@+id/tvItemStaffName"
        style="@style/text_12_333"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:text="" />

    <TextView
        android:id="@+id/tvItemStatus"
        style="@style/text_12_333"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:text="" />

</LinearLayout>