<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/linOrderInfo"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:visibility="gone"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_white_top_right_5">

        <ImageView
            android:id="@+id/ivOrderInfoBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:padding="@dimen/dp10"
            android:src="@mipmap/ic_back" />

        <TextView
            style="@style/text_16_333"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:text="订单详情"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivOrderInfoClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:padding="@dimen/dp10"
            android:src="@mipmap/ic_close001" />

    </RelativeLayout>

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/srlOrderInfo"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <com.scwang.smart.refresh.header.ClassicsHeader
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/dp10">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/ivOrderInfoPayType"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp5" />

                    <TextView
                        android:id="@+id/tvOrderInfoPayType"
                        style="@style/text_12_333"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:text=""
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvOrderInfoStatus"
                        style="@style/text_12_333"
                        android:text="" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp5"
                    android:background="@drawable/shape_white_5"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp5">

                    <TextView
                        style="@style/text_12_666"
                        android:text="订单金额：" />

                    <TextView
                        android:id="@+id/tvOrderInfoTotal"
                        style="@style/text_12_333"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:text="￥0.00" />

                    <ImageView
                        android:id="@+id/ivOrderInfoQuestion"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:padding="@dimen/dp5"
                        android:src="@mipmap/ic_question001"
                        android:visibility="gone" />

                    <TextView
                        style="@style/text_12_666"
                        android:text="优惠金额：" />

                    <TextView
                        android:id="@+id/tvOrderInfoDisCount"
                        style="@style/text_12_333"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:text="￥0.00" />

                    <TextView
                        style="@style/text_12_666"
                        android:text="实收金额：" />

                    <TextView
                        android:id="@+id/tvOrderInfoRealTotal"
                        style="@style/text_12_333"
                        android:text="￥0.00" />

                </LinearLayout>

                <TextView
                    style="@style/text_12_666"
                    android:layout_marginTop="@dimen/dp10"
                    android:text="订单信息" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp5"
                    android:background="@drawable/shape_white_5"
                    android:orientation="vertical"
                    android:padding="@dimen/dp10">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/dp10"
                            android:layout_weight="3"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <TextView
                                style="@style/text_12_666"
                                android:text="订单编号：" />

                            <TextView
                                android:id="@+id/tvOrderInfoNo"
                                style="@style/text_12_333"
                                android:text="" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="2"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <TextView
                                style="@style/text_12_666"
                                android:text="订单来源：" />

                            <TextView
                                android:id="@+id/tvOrderInfoSource"
                                style="@style/text_12_333"
                                android:text="" />

                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp10"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/dp10"
                            android:layout_weight="3"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <TextView
                                style="@style/text_12_666"
                                android:text="商户单号：" />

                            <TextView
                                android:id="@+id/tvOrderInfoTradeNo"
                                style="@style/text_12_333"
                                android:text="" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="2"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <TextView
                                style="@style/text_12_666"
                                android:text="支付时间：" />

                            <TextView
                                android:id="@+id/tvOrderInfoPayTime"
                                style="@style/text_12_333"
                                android:text="" />

                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp10"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/dp10"
                            android:layout_weight="3"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <TextView
                                style="@style/text_12_666"
                                android:text="会员名称：" />

                            <TextView
                                android:id="@+id/tvCusName"
                                style="@style/text_12_333"
                                android:text="" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="2"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <TextView
                                style="@style/text_12_666"
                                android:text="手机号码：" />

                            <TextView
                                android:id="@+id/tvCusPhone"
                                style="@style/text_12_333"
                                android:text="" />

                            <TextView
                                style="@style/text_12_666"
                                android:text="会员等级："
                                android:visibility="gone" />

                            <ImageView
                                android:id="@+id/ivCusLevel"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="@dimen/dp5"
                                android:src="@mipmap/ic_medal001"
                                android:visibility="gone" />

                            <TextView
                                android:id="@+id/tvCusLevel"
                                style="@style/text_12_333"
                                android:text="--"
                                android:visibility="gone" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/linOrderInfoRefund"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_666"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:text="退款信息" />

                        <TextView
                            style="@style/text_12_666"
                            android:text="共退款：" />

                        <TextView
                            android:id="@+id/tvOrderInfoRefundTotal"
                            style="@style/text_12_red"
                            android:text="￥0.00"
                            android:textStyle="bold" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp5"
                        android:background="@drawable/shape_e7eae7_top_5"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:paddingVertical="@dimen/dp5">

                        <TextView
                            style="@style/text_12_666"
                            android:layout_width="0dp"
                            android:layout_weight="1.5"
                            android:gravity="center"
                            android:text="退款单号" />

                        <TextView
                            style="@style/text_12_666"
                            android:layout_width="0dp"
                            android:layout_weight="1.5"
                            android:gravity="center"
                            android:text="退款时间" />

                        <TextView
                            style="@style/text_12_666"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:text="退款金额" />

                        <TextView
                            style="@style/text_12_666"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:text="操作人" />

                        <TextView
                            style="@style/text_12_666"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:text="退款状态  " />

                    </LinearLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvOrderInfo"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent" />

                </LinearLayout>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

        <com.scwang.smart.refresh.footer.ClassicsFooter
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    <TextView
        android:id="@+id/tvOrderInfoRefund"
        style="@style/text_14_white"
        android:layout_width="@dimen/dp89"
        android:layout_height="@dimen/dp30"
        android:layout_gravity="right"
        android:layout_margin="@dimen/dp10"
        android:background="@drawable/shape_red_5"
        android:gravity="center"
        android:text="退款"
        android:visibility="gone" />

</LinearLayout>