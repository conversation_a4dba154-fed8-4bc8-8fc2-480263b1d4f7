<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/linTotal"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent"
    android:orientation="vertical"
    android:visibility="gone">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_white_top_right_5"
        android:gravity="center"
        android:orientation="horizontal">

        <TextView
            style="@style/text_16_333"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:padding="@dimen/dp10"
            android:text="汇总"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivTotalClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp10"
            android:src="@mipmap/ic_close001" />

    </LinearLayout>

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/srlTotal"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/transparent">

        <com.scwang.smart.refresh.header.ClassicsHeader
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/transparent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingHorizontal="@dimen/dp10">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvDay0"
                        style="@style/text_12_333"
                        android:layout_width="0dp"
                        android:layout_marginEnd="@dimen/dp5"
                        android:layout_weight="1"
                        android:background="@drawable/shape_green_kuang_5"
                        android:gravity="center"
                        android:paddingVertical="@dimen/dp5"
                        android:text="今日"
                        android:textColor="@color/green" />

                    <TextView
                        android:id="@+id/tvDay1"
                        style="@style/text_12_333"
                        android:layout_width="0dp"
                        android:layout_marginEnd="@dimen/dp5"
                        android:layout_weight="1"
                        android:background="@drawable/shape_e1e2e3_kuang_5"
                        android:gravity="center"
                        android:paddingVertical="@dimen/dp5"
                        android:text="昨日" />

                    <TextView
                        android:id="@+id/tvDay2"
                        style="@style/text_12_333"
                        android:layout_width="0dp"
                        android:layout_marginEnd="@dimen/dp5"
                        android:layout_weight="1"
                        android:background="@drawable/shape_e1e2e3_kuang_5"
                        android:gravity="center"
                        android:paddingVertical="@dimen/dp5"
                        android:text="近7日" />

                    <TextView
                        android:id="@+id/tvDay3"
                        style="@style/text_12_333"
                        android:layout_width="0dp"
                        android:layout_marginEnd="@dimen/dp5"
                        android:layout_weight="1"
                        android:background="@drawable/shape_e1e2e3_kuang_5"
                        android:gravity="center"
                        android:paddingVertical="@dimen/dp5"
                        android:text="近15日" />

                    <LinearLayout
                        android:id="@+id/linTotalStartTime"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="2"
                        android:background="@drawable/shape_e1e2e3_kuang_5"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:padding="@dimen/dp5">

                        <TextView
                            android:id="@+id/tvTotalStartTime"
                            style="@style/text_12_333"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:hint="开始时间"
                            tools:ignore="NestedWeights" />

                        <ImageView
                            android:id="@+id/ivTotalStartTime"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@mipmap/ic_arrow001" />

                    </LinearLayout>

                    <TextView
                        style="@style/text_14_999"
                        android:layout_margin="@dimen/dp5"
                        android:text="~" />

                    <LinearLayout
                        android:id="@+id/linTotalEndTime"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="2"
                        android:background="@drawable/shape_e1e2e3_kuang_5"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:padding="@dimen/dp5">

                        <TextView
                            android:id="@+id/tvTotalEndTime"
                            style="@style/text_12_333"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:hint="结束时间"
                            tools:ignore="NestedWeights" />

                        <ImageView
                            android:id="@+id/ivTotalEndTime"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@mipmap/ic_arrow001" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp5"
                        android:layout_weight="1"
                        android:background="@drawable/shape_white_5"
                        android:orientation="vertical"
                        android:padding="@dimen/dp8">

                        <TextView
                            style="@style/text_12_333"
                            android:text="实收金额(元)"
                            android:textColor="@color/green" />

                        <TextView
                            android:id="@+id/tvTotalRealTotal"
                            style="@style/text_16_333"
                            android:layout_marginTop="@dimen/dp5"
                            android:text="0.00"
                            android:textColor="@color/green"
                            android:textStyle="bold" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp5"
                        android:layout_weight="1"
                        android:background="@drawable/shape_white_5"
                        android:orientation="vertical"
                        android:padding="@dimen/dp8">

                        <TextView
                            style="@style/text_12_333"
                            android:text="交易金额(元)"
                            android:textColor="@color/orange" />

                        <TextView
                            android:id="@+id/tvTotalTotal"
                            style="@style/text_16_333"
                            android:layout_marginTop="@dimen/dp5"
                            android:text="0.00"
                            android:textStyle="bold" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp5"
                        android:layout_weight="1"
                        android:background="@drawable/shape_white_5"
                        android:orientation="vertical"
                        android:padding="@dimen/dp8">

                        <TextView
                            style="@style/text_12_333"
                            android:text="优惠金额(元)"
                            android:textColor="@color/red" />

                        <TextView
                            android:id="@+id/tvTotalDiscount"
                            style="@style/text_16_333"
                            android:layout_marginTop="@dimen/dp5"
                            android:text="0.00"
                            android:textStyle="bold" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/shape_white_5"
                        android:orientation="vertical"
                        android:padding="@dimen/dp8">

                        <TextView
                            style="@style/text_12_999"
                            android:text="退款金额(元)" />

                        <TextView
                            android:id="@+id/tvTotalRefund"
                            style="@style/text_16_333"
                            android:layout_marginTop="@dimen/dp5"
                            android:text="0.00"
                            android:textStyle="bold" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:background="@drawable/shape_e7eae7_top_5"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:paddingVertical="@dimen/dp5">

                    <TextView
                        style="@style/text_12_666"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="收款方式" />

                    <TextView
                        style="@style/text_12_666"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="实收金额" />

                    <TextView
                        style="@style/text_12_666"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="交易金额" />

                    <TextView
                        style="@style/text_12_666"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="优惠金额" />

                    <TextView
                        style="@style/text_12_666"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="退款金额" />

                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvTotal"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />

                <LinearLayout
                    android:id="@+id/linTotalEmpty"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:paddingTop="@dimen/dp40"
                    android:visibility="gone">

                    <ImageView
                        android:id="@+id/ivTotalEmpty"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@mipmap/ic_empty" />

                    <TextView
                        android:id="@+id/tvTotalEmpty"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp26"
                        android:textColor="@color/color_666"
                        android:textSize="@dimen/f14"
                        android:textStyle="bold" />

                </LinearLayout>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

        <com.scwang.smart.refresh.footer.ClassicsFooter
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

</LinearLayout>