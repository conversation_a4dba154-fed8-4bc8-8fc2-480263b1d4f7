<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/linToast"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    android:layout_marginTop="@dimen/dp60"
    android:orientation="horizontal"
    android:paddingHorizontal="@dimen/dp10"
    android:paddingVertical="@dimen/dp5">

    <ImageView
        android:id="@+id/ivToast"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp5" />

    <TextView
        android:id="@+id/tvToast"
        style="@style/text_14_white"
        android:text="" />

</LinearLayout>