<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/linItem"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/color_f7"
    android:gravity="center"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/linItemOrder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvItemNo"
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_weight="2"
            android:gravity="center"
            android:paddingVertical="@dimen/dp6"
            android:text="" />

        <ImageView
            android:id="@+id/ivItemRefund"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp5"
            android:src="@mipmap/ic_refund001"
            android:visibility="invisible" />

        <TextView
            android:id="@+id/tvItemTime"
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_weight="2"
            android:gravity="center"
            android:paddingVertical="@dimen/dp6"
            android:text="" />

        <TextView
            android:id="@+id/tvItemTotal"
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:paddingVertical="@dimen/dp6"
            android:text="￥0.00" />

        <TextView
            android:id="@+id/tvItemRealTotal"
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:paddingVertical="@dimen/dp6"
            android:text="￥0.00" />

        <TextView
            android:id="@+id/tvItemStatus"
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:paddingVertical="@dimen/dp6"
            android:text="" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/linItemRefund"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal"
        android:visibility="gone">

        <TextView
            android:id="@+id/tvItemRefundNo"
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_weight="2"
            android:gravity="center"
            android:paddingVertical="@dimen/dp6"
            android:text="" />

        <TextView
            android:id="@+id/tvItemRefundTime"
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_weight="2"
            android:gravity="center"
            android:paddingVertical="@dimen/dp6"
            android:text="" />

        <TextView
            android:id="@+id/tvItemRefundTotal"
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:paddingVertical="@dimen/dp6"
            android:text="￥0.00" />

        <TextView
            android:id="@+id/tvItemStaffName"
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:paddingVertical="@dimen/dp6"
            android:text="" />

        <TextView
            android:id="@+id/tvItemRefundStatus"
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:paddingVertical="@dimen/dp6"
            android:text="" />

    </LinearLayout>
</LinearLayout>