<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/linSetting"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:visibility="gone"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_white_top_right_5"
        android:gravity="center"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvSettingBase"
            style="@style/text_16_333"
            android:layout_gravity="bottom"
            android:layout_marginStart="@dimen/dp10"
            android:background="@drawable/shape_f7_top_5"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp6"
            android:text="基础设置"
            android:textColor="@color/green"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvSettingCashier"
            style="@style/text_16_333"
            android:layout_gravity="bottom"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp6"
            android:text="收银设置"
            android:textStyle="bold" />

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <ImageView
            android:id="@+id/ivSettingClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp10"
            android:src="@mipmap/ic_close001" />

    </LinearLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/dp10">

            <!--基础设置-->
            <LinearLayout
                android:id="@+id/linSettingBase"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    style="@style/text_12_666"
                    android:text="基础设置" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp5"
                    android:background="@drawable/shape_white_5"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="@dimen/dp10">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_14_666"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:text="商户名称：" />

                        <TextView
                            android:id="@+id/tvShopName"
                            style="@style/text_14_999"
                            android:layout_height="match_parent"
                            android:text="" />

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp1"
                        android:layout_marginTop="@dimen/dp10"
                        android:background="@color/color_e1e2e3" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp10"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_14_666"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:text="店铺名称：" />

                        <TextView
                            android:id="@+id/tvBranchName"
                            style="@style/text_14_999"
                            android:text="" />

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp1"
                        android:layout_marginTop="@dimen/dp10"
                        android:background="@color/color_e1e2e3" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp10"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_14_666"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:text="员工名称：" />

                        <TextView
                            android:id="@+id/tvStaffName"
                            style="@style/text_14_999"
                            android:text="" />

                    </LinearLayout>

                </LinearLayout>

                <TextView
                    style="@style/text_12_666"
                    android:layout_marginTop="@dimen/dp10"
                    android:text="启动设置" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp5"
                    android:background="@drawable/shape_white_5"
                    android:orientation="vertical"
                    android:padding="@dimen/dp5">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <ImageView
                            android:id="@+id/ivStartUp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:padding="@dimen/dp5"
                            android:src="@drawable/checkbox_style" />

                        <TextView
                            style="@style/text_14_333"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:padding="@dimen/dp5"
                            android:text="开机启动" />

                        <ImageView
                            android:id="@+id/ivAuto"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:padding="@dimen/dp5"
                            android:src="@drawable/checkbox_style" />

                        <TextView
                            style="@style/text_14_333"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:padding="@dimen/dp5"
                            android:text="自动登录" />

                        <ImageView
                            android:id="@+id/ivPayFinish"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:padding="@dimen/dp5"
                            android:src="@drawable/checkbox_style" />

                        <TextView
                            style="@style/text_14_333"
                            android:layout_marginEnd="@dimen/dp5"
                            android:padding="@dimen/dp5"
                            android:text="结算完成最小化" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp5"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <ImageView
                            android:id="@+id/ivRefundFinish"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:padding="@dimen/dp5"
                            android:src="@drawable/checkbox_style" />

                        <TextView
                            style="@style/text_14_333"
                            android:padding="@dimen/dp5"
                            android:text="退款完成最小化" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

            <!--收银设置-->
            <LinearLayout
                android:id="@+id/linSettingCashier"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <TextView
                    style="@style/text_12_666"
                    android:text="设置收银金额识别区域" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp5"
                    android:background="@drawable/shape_white_5"
                    android:padding="@dimen/dp10">

                    <ImageView
                        android:id="@+id/ivOcr"
                        android:layout_width="@dimen/dp106"
                        android:layout_height="@dimen/dp42"
                        android:background="@drawable/shape_e1e2e3_kuang_5"
                        android:padding="@dimen/dp1"
                        android:src="@mipmap/ic_default_img" />

                    <TextView
                        android:id="@+id/tvOcr"
                        style="@style/text_14_white"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:background="@drawable/shape_green_5"
                        android:padding="@dimen/dp6"
                        android:text="设置识别区域" />

                </RelativeLayout>

                <!--退款审核-->
                <LinearLayout
                    android:id="@+id/linProcess"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:orientation="vertical">

                    <TextView
                        style="@style/text_12_666"
                        android:text="退款审核" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp5"
                        android:background="@drawable/shape_white_5"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:padding="@dimen/dp5">

                        <ImageView
                            android:id="@+id/ivProcess0"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:padding="@dimen/dp5"
                            android:src="@drawable/checkbox_style" />

                        <TextView
                            style="@style/text_14_333"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:padding="@dimen/dp5"
                            android:text="审核" />

                        <ImageView
                            android:id="@+id/ivProcess1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:padding="@dimen/dp5"
                            android:src="@drawable/checkbox_style" />

                        <TextView
                            style="@style/text_14_333"
                            android:layout_width="0dp"
                            android:layout_weight="2"
                            android:padding="@dimen/dp5"
                            android:text="不审核" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center|right"
                android:orientation="horizontal"
                android:paddingVertical="@dimen/dp10">

                <TextView
                    android:id="@+id/tvExit"
                    style="@style/text_14_white"
                    android:layout_width="@dimen/dp89"
                    android:layout_height="@dimen/dp30"
                    android:layout_gravity="right"
                    android:layout_marginEnd="@dimen/dp10"
                    android:background="@drawable/shape_yellow_5"
                    android:gravity="center"
                    android:text="退出程序" />

                <TextView
                    android:id="@+id/tvVersion"
                    style="@style/text_14_white"
                    android:layout_width="@dimen/dp89"
                    android:layout_height="@dimen/dp30"
                    android:layout_gravity="right"
                    android:layout_marginEnd="@dimen/dp10"
                    android:background="@drawable/shape_blue_5"
                    android:gravity="center"
                    android:text="检查更新" />

                <TextView
                    android:id="@+id/tvLoginOut"
                    style="@style/text_14_white"
                    android:layout_width="@dimen/dp89"
                    android:layout_height="@dimen/dp30"
                    android:layout_gravity="right"
                    android:background="@drawable/shape_red_5"
                    android:gravity="center"
                    android:text="退出登录" />

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

</LinearLayout>