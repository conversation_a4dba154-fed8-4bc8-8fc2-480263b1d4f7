<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_white_5"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp2"
        android:layout_marginBottom="@dimen/dp2">

        <TextView
            android:id="@+id/tvDialogCancel"
            style="@style/text_12_999"
            android:padding="@dimen/dp10"
            android:text="取消" />

        <TextView
            style="@style/text_14_333"
            android:layout_centerInParent="true"
            android:text="选择日期"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvDialogConfirm"
            style="@style/text_12_blue"
            android:layout_alignParentEnd="true"
            android:padding="@dimen/dp10"
            android:text="确认" />

    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp1"
        android:background="@color/color_e1e2e3" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/dp10"
        android:paddingRight="@dimen/dp10">

        <LinearLayout
            android:id="@+id/linDialogDateStart"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvDialogDateStart"
                style="@style/text_12_blue"
                android:layout_marginTop="@dimen/dp10"
                android:text="选择开始时间" />

            <View
                android:id="@+id/vDialogDateStart"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp1"
                android:layout_marginTop="@dimen/dp5"
                android:background="@color/blue" />

        </LinearLayout>

        <View
            android:layout_width="@dimen/dp10"
            android:layout_height="@dimen/dp1"
            android:layout_margin="@dimen/dp6"
            android:background="@color/color_333" />

        <LinearLayout
            android:id="@+id/linDialogDateEnd"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvDialogDateEnd"
                style="@style/text_12_999"
                android:layout_marginTop="@dimen/dp10"
                android:text="选择结束时间" />

            <View
                android:id="@+id/vDialogDateEnd"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp1"
                android:layout_marginTop="@dimen/dp5"
                android:background="@color/color_e1e2e3" />

        </LinearLayout>

    </LinearLayout>

    <com.yxl.cashier.helper.view.pickerview.DateTimeWheelLayout
        android:id="@+id/wheelLayout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp100"
        app:wheel_dateMode="year_month_day" />

</LinearLayout>