<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="@dimen/dp315"
        android:layout_height="@dimen/dp287"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_green_top_5"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                style="@style/text_14_white"
                android:layout_width="0dp"
                android:layout_marginStart="@dimen/dp10"
                android:layout_weight="1"
                android:text="退款审批"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/ivPopupClose"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp5"
                android:padding="@dimen/dp5"
                android:src="@mipmap/ic_close004" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/shape_f7_bottom_5"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/ivPopupStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

            <TextView
                android:id="@+id/tvPopupStatus"
                style="@style/text_12_red"
                android:layout_marginTop="@dimen/dp15"
                android:text=""
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvPopupMoney"
                style="@style/text_12_green"
                android:layout_marginTop="@dimen/dp5"
                android:text="退款金额：￥0.00"
                android:textStyle="bold"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tvPopupTips"
                style="@style/text_10_666"
                android:layout_marginTop="@dimen/dp5"
                android:gravity="center"
                android:text="失败原因:\n" />

            <TextView
                android:id="@+id/tvPopupDownTimer"
                style="@style/text_12_red"
                android:layout_marginTop="@dimen/dp5"
                android:text="5秒后关闭窗口"
                android:visibility="gone" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp20"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvPopupBack"
                    android:layout_width="@dimen/dp89"
                    android:layout_height="@dimen/dp30"
                    android:layout_marginHorizontal="@dimen/dp8"
                    android:background="@drawable/shape_green_kuang_5"
                    android:gravity="center"
                    android:text="返回"
                    android:textColor="@color/green"
                    android:textSize="@dimen/f12" />

                <TextView
                    android:id="@+id/tvPopupAgain"
                    style="@style/text_12_white"
                    android:layout_width="@dimen/dp89"
                    android:layout_height="@dimen/dp30"
                    android:layout_marginHorizontal="@dimen/dp8"
                    android:background="@drawable/shape_red_5"
                    android:gravity="center"
                    android:text="重新退款"
                    android:visibility="gone" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</LinearLayout>