<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent"
    android:clickable="true"
    android:focusable="true"
    android:orientation="horizontal">

    <EditText
        android:id="@+id/etTest"
        android:layout_width="1dp"
        android:layout_height="1dp"
        android:hint=""
        android:maxLines="1"
        android:textSize="@dimen/f8" />

    <View
        android:id="@+id/v"
        android:layout_width="0dp"
        android:layout_height="0dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:background="@drawable/shape_green_left_5"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/ivLogo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp10"
                android:src="@mipmap/ic_main_img001" />

            <LinearLayout
                android:id="@+id/lin0"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp5"
                android:layout_marginTop="@dimen/dp10"
                android:background="@drawable/shape_white_left_22"
                android:gravity="center"
                android:orientation="horizontal"
                android:padding="@dimen/dp10">

                <ImageView
                    android:id="@+id/iv0"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@mipmap/ic_tab_cashier001" />

                <TextView
                    android:id="@+id/tv0"
                    style="@style/text_14_white"
                    android:layout_marginLeft="@dimen/dp6"
                    android:text="收银"
                    android:textColor="@color/green"
                    android:textStyle="bold" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/lin1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp5"
                android:gravity="center"
                android:orientation="horizontal"
                android:padding="@dimen/dp10">

                <ImageView
                    android:id="@+id/iv1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@mipmap/ic_tab_refund002" />

                <TextView
                    android:id="@+id/tv1"
                    style="@style/text_14_white"
                    android:layout_marginLeft="@dimen/dp6"
                    android:text="退款"
                    android:textStyle="bold" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/lin2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp5"
                android:gravity="center"
                android:orientation="horizontal"
                android:padding="@dimen/dp10">

                <ImageView
                    android:id="@+id/iv2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@mipmap/ic_tab_order002" />

                <TextView
                    android:id="@+id/tv2"
                    style="@style/text_14_white"
                    android:layout_marginStart="@dimen/dp6"
                    android:text="订单"
                    android:textStyle="bold" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/lin3"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp5"
                android:gravity="center"
                android:orientation="horizontal"
                android:padding="@dimen/dp10">

                <ImageView
                    android:id="@+id/iv3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@mipmap/ic_tab_total002" />

                <TextView
                    android:id="@+id/tv3"
                    style="@style/text_14_white"
                    android:layout_marginStart="@dimen/dp6"
                    android:text="汇总"
                    android:textStyle="bold" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/lin4"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp5"
                android:gravity="center"
                android:orientation="horizontal"
                android:padding="@dimen/dp10">

                <ImageView
                    android:id="@+id/iv4"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@mipmap/ic_tab_setting002" />

                <TextView
                    android:id="@+id/tv4"
                    style="@style/text_14_white"
                    android:layout_marginStart="@dimen/dp6"
                    android:text="设置"
                    android:textStyle="bold" />

            </LinearLayout>

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="1" />

            <TextView
                android:id="@+id/tvVersionName"
                style="@style/text_14_white"
                android:text="v" />

            <ImageView
                android:id="@+id/ivStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp10"
                android:src="@mipmap/ic_style001" />

        </LinearLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/shape_f7_right_5"
            android:orientation="vertical">

            <include layout="@layout/include_cashier" />

            <include layout="@layout/include_refund" />

            <include layout="@layout/include_order" />

            <include layout="@layout/include_total" />

            <include layout="@layout/include_setting" />

            <include layout="@layout/include_order_info" />

        </FrameLayout>

    </LinearLayout>

    <!--收银结果-->
    <include layout="@layout/include_cashier_result" />

    <include layout="@layout/layout_toast" />

    <!--loadingView-->
    <include layout="@layout/include_loading" />


</FrameLayout>