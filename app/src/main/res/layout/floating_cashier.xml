<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent"
    android:orientation="vertical">

    <EditText
        android:id="@+id/etTest"
        android:layout_width="1dp"
        android:layout_height="1dp"
        android:hint=""
        android:maxLines="1"
        android:textSize="@dimen/f8" />

    <LinearLayout
        android:id="@+id/linCrop"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/shape_white_5"
        android:gravity="center"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/ivIcon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_green_left_5"
            android:padding="@dimen/dp10"
            android:src="@mipmap/ic_login001" />

        <LinearLayout
            android:id="@+id/linMoney"
            android:layout_width="@dimen/dp120"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvUnit"
                style="@style/text_12_white"
                android:layout_marginTop="@dimen/dp2"
                android:gravity="center"
                android:maxLines="1"
                android:text="￥"
                android:textColor="@color/green"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvMoney"
                style="@style/text_18_white"
                android:gravity="center"
                android:maxLines="1"
                android:text="0.00"
                android:textColor="@color/green"
                android:textStyle="bold" />

        </LinearLayout>

        <ImageView
            android:id="@+id/ivFocus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp6"
            android:src="@drawable/checkbox_style" />

    </LinearLayout>

    <ImageView
        android:id="@+id/ivImg"
        android:layout_width="100dp"
        android:layout_height="@dimen/dp40"
        android:layout_below="@+id/linCrop"
        android:background="@color/black"
        android:visibility="gone" />

</RelativeLayout>