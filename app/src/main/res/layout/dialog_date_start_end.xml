<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_white_5"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp2"
        android:layout_marginBottom="@dimen/dp2">

        <TextView
            android:id="@+id/tvDialogCancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp16"
            android:text="取消"
            android:textColor="@color/color_999"
            android:textSize="@dimen/f16" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="选择日期"
            android:textColor="@color/black"
            android:textSize="@dimen/f18"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvDialogConfirm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:padding="@dimen/dp16"
            android:text="确认"
            android:textColor="@color/blue"
            android:textSize="@dimen/f16" />

    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp1"
        android:background="@color/color_e1e2e3" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/dp16"
        android:paddingRight="@dimen/dp16">

        <LinearLayout
            android:id="@+id/linDialogDateStart"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvDialogDateStart"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp14"
                android:text="选择开始时间"
                android:textColor="@color/blue"
                android:textSize="@dimen/f16" />

            <View
                android:id="@+id/vDialogDateStart"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp1"
                android:layout_marginTop="@dimen/dp13"
                android:background="@color/blue" />

        </LinearLayout>

        <View
            android:layout_width="@dimen/dp10"
            android:layout_height="@dimen/dp1"
            android:layout_margin="@dimen/dp6"
            android:background="@color/color_333" />

        <LinearLayout
            android:id="@+id/linDialogDateEnd"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvDialogDateEnd"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp14"
                android:text="选择结束时间"
                android:textColor="@color/color_999"
                android:textSize="@dimen/f16" />

            <View
                android:id="@+id/vDialogDateEnd"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp1"
                android:layout_marginTop="@dimen/dp13"
                android:background="@color/color_e1e2e3" />

        </LinearLayout>

    </LinearLayout>

    <com.yxl.cashier.helper.view.pickerview.DateTimeWheelLayout
        android:id="@+id/wheelLayout"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        app:wheel_dateMode="year_month_day" />

</LinearLayout>