<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/linCashier"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:visibility="visible">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_white_top_right_5"
        android:gravity="center"
        android:orientation="horizontal">

        <TextView
            style="@style/text_16_333"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:padding="@dimen/dp10"
            android:text="收银"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivCashierClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp10"
            android:src="@mipmap/ic_close001" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp45"
        android:layout_marginTop="@dimen/dp15"
        android:background="@drawable/shape_white_5"
        android:gravity="center"
        android:orientation="horizontal">

        <TextView
            style="@style/text_14_666"
            android:layout_marginStart="@dimen/dp10"
            android:text="收银金额" />

        <FrameLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1">

            <TextView
                android:id="@+id/tvCashierMoney"
                style="@style/text_14_333"
                android:layout_width="match_parent"
                android:background="@null"
                android:gravity="right"
                android:hint="请输入收银金额"
                android:maxLines="1"
                android:padding="@dimen/dp10" />

            <ImageView
                android:id="@+id/ivCashierMoney"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp18"
                android:layout_gravity="center_vertical|right"
                android:layout_marginEnd="@dimen/dp5"
                android:src="@drawable/anim_cursor" />

        </FrameLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp45"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/shape_white_5"
        android:gravity="center"
        android:orientation="horizontal">

        <TextView
            style="@style/text_14_666"
            android:layout_marginStart="@dimen/dp10"
            android:text="付款码" />

        <FrameLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1">

            <EditText
                android:id="@+id/etCashierCode"
                style="@style/text_14_333"
                android:layout_width="match_parent"
                android:background="@drawable/shape_white_5"
                android:cursorVisible="false"
                android:editable="false"
                android:focusable="true"
                android:focusableInTouchMode="false"
                android:gravity="right"
                android:hint="请扫描/输入客户付款码"
                android:inputType="none"
                android:maxLines="1"
                android:padding="@dimen/dp10" />

            <ImageView
                android:id="@+id/ivCashierCode"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp18"
                android:layout_gravity="center_vertical|right"
                android:layout_marginEnd="@dimen/dp5"
                android:src="@drawable/anim_cursor"
                android:visibility="gone" />

        </FrameLayout>

    </LinearLayout>

    <View
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <com.yxl.cashier.helper.view.NumberKeyBoardView
        android:id="@+id/cashierKeyBoardView"
        android:layout_width="match_parent"
        android:layout_marginHorizontal="@dimen/dp45"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        app:confirm="1" />

</LinearLayout>