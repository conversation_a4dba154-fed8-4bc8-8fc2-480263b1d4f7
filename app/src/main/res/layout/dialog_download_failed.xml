<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent"
    android:gravity="center"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="@dimen/dp300"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_white_5"
        android:gravity="center"
        android:orientation="vertical">

        <TextView
            style="@style/text_16_333"
            android:layout_marginVertical="@dimen/dp16"
            android:text="下载失败" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp1"
            android:background="@color/color_e1e2e3" />

        <TextView
            style="@style/text_14_999"
            android:padding="@dimen/dp10"
            android:text="请确保网络状况良好，下载过程中不要关闭当前页面" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp1"
            android:background="@color/color_e1e2e3" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@id/versionchecklib_failed_dialog_cancel"
                style="@style/text_14_666"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:paddingVertical="@dimen/dp10"
                android:text="取消" />

            <View
                android:layout_width="@dimen/dp1"
                android:layout_height="match_parent"
                android:background="@color/color_e1e2e3" />

            <TextView
                android:id="@id/versionchecklib_failed_dialog_retry"
                style="@style/text_14_blue"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:paddingVertical="@dimen/dp10"
                android:text="重试" />


        </LinearLayout>
    </LinearLayout>
</LinearLayout>