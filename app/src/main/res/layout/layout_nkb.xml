<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv7"
                android:layout_width="@dimen/dp59"
                android:layout_height="@dimen/dp51"
                android:layout_margin="@dimen/dp1"
                android:background="@drawable/button"
                android:gravity="center"
                android:text="7"
                android:textColor="@drawable/button_text_color"
                android:textSize="@dimen/f26"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv8"
                android:layout_width="@dimen/dp59"
                android:layout_height="@dimen/dp51"
                android:layout_margin="@dimen/dp1"
                android:background="@drawable/button"
                android:gravity="center"
                android:text="8"
                android:textColor="@drawable/button_text_color"
                android:textSize="@dimen/f26"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv9"
                android:layout_width="@dimen/dp59"
                android:layout_height="@dimen/dp51"
                android:layout_margin="@dimen/dp1"
                android:background="@drawable/button"
                android:gravity="center"
                android:text="9"
                android:textColor="@drawable/button_text_color"
                android:textSize="@dimen/f26"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv0"
                android:layout_width="@dimen/dp59"
                android:layout_height="@dimen/dp51"
                android:layout_margin="@dimen/dp1"
                android:background="@drawable/button"
                android:gravity="center"
                android:text="0"
                android:textColor="@drawable/button_text_color"
                android:textSize="@dimen/s26"
                android:textStyle="bold" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv4"
                android:layout_width="@dimen/dp59"
                android:layout_height="@dimen/dp51"
                android:layout_margin="@dimen/dp1"
                android:background="@drawable/button"
                android:gravity="center"
                android:text="4"
                android:textColor="@drawable/button_text_color"
                android:textSize="@dimen/f26"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv5"
                android:layout_width="@dimen/dp59"
                android:layout_height="@dimen/dp51"
                android:layout_margin="@dimen/dp1"
                android:background="@drawable/button"
                android:gravity="center"
                android:text="5"
                android:textColor="@drawable/button_text_color"
                android:textSize="@dimen/f26"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv6"
                android:layout_width="@dimen/dp59"
                android:layout_height="@dimen/dp51"
                android:layout_margin="@dimen/dp1"
                android:background="@drawable/button"
                android:gravity="center"
                android:text="6"
                android:textColor="@drawable/button_text_color"
                android:textSize="@dimen/f26"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvDrop"
                android:layout_width="@dimen/dp59"
                android:layout_height="@dimen/dp51"
                android:layout_margin="@dimen/dp1"
                android:background="@drawable/button"
                android:gravity="center"
                android:text="•"
                android:textColor="@drawable/button_text_color"
                android:textSize="@dimen/f26"
                android:textStyle="bold" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv1"
                android:layout_width="@dimen/dp59"
                android:layout_height="@dimen/dp51"
                android:layout_margin="@dimen/dp1"
                android:background="@drawable/button"
                android:gravity="center"
                android:text="1"
                android:textColor="@drawable/button_text_color"
                android:textSize="@dimen/f26"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv2"
                android:layout_width="@dimen/dp59"
                android:layout_height="@dimen/dp51"
                android:layout_margin="@dimen/dp1"
                android:background="@drawable/button"
                android:gravity="center"
                android:text="2"
                android:textColor="@drawable/button_text_color"
                android:textSize="@dimen/f26"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv3"
                android:layout_width="@dimen/dp59"
                android:layout_height="@dimen/dp51"
                android:layout_margin="@dimen/dp1"
                android:background="@drawable/button"
                android:gravity="center"
                android:text="3"
                android:textColor="@drawable/button_text_color"
                android:textSize="@dimen/f26"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvClear"
                android:layout_width="@dimen/dp59"
                android:layout_height="@dimen/dp51"
                android:layout_margin="@dimen/dp1"
                android:background="@drawable/button"
                android:contentDescription="删除"
                android:gravity="center"
                android:text="清空"
                android:textColor="@drawable/button_text_color"
                android:textSize="@dimen/f16"
                android:textStyle="bold" />

        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/linBack"
            android:layout_width="@dimen/dp59"
            android:layout_height="@dimen/dp51"
            android:layout_margin="@dimen/dp1"
            android:background="@drawable/shape_e1e2e3_kuang_5"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@mipmap/ic_close003" />

        </LinearLayout>


        <TextView
            android:id="@+id/tvConfirm"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="@dimen/dp1"
            android:background="@drawable/shape_green_5"
            android:gravity="center"
            android:text="确认"
            android:textColor="@color/white"
            android:textSize="@dimen/f16"
            android:textStyle="bold" />

    </LinearLayout>

</LinearLayout>