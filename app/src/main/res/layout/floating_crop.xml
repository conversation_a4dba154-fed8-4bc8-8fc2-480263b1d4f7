<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/flCrop"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="horizontal">

    <com.yxl.cashier.helper.view.CropImageView
        android:id="@+id/civImg"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <ImageView
        android:id="@+id/ivImg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <LinearLayout
        android:id="@+id/linCrop"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="right"
        android:layout_margin="@dimen/dp20"
        android:gravity="center"
        android:orientation="horizontal"
        android:visibility="gone">

        <ImageView
            android:id="@+id/ivIcon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_green_left_5"
            android:padding="@dimen/dp10"
            android:src="@mipmap/ic_login001"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tvConfirm"
            style="@style/text_14_white"
            android:layout_height="@dimen/dp48"
            android:layout_marginEnd="@dimen/dp10"
            android:background="@drawable/shape_green_left_5"
            android:drawableLeft="@mipmap/ic_crop001"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp15"
            android:text="确认识别区域"
            tools:ignore="RtlHardcoded" />

        <ImageView
            android:id="@+id/ivClose"
            android:layout_width="@dimen/dp48"
            android:layout_height="@dimen/dp48"
            android:background="@drawable/shape_red_5"
            android:padding="@dimen/dp10"
            android:src="@mipmap/ic_close004" />

    </LinearLayout>

</FrameLayout>