<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/linPopup"
    android:layout_width="@dimen/dp468"
    android:layout_height="@dimen/dp324"
    android:background="@drawable/shape_black_tm_5"
    android:clickable="true"
    android:focusable="true"
    android:gravity="center"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="@dimen/dp200"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_white_5"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvPopupMsg"
            style="@style/text_12_333"
            android:layout_margin="@dimen/dp16"
            android:gravity="center"
            android:text="提示"
            android:textStyle="bold" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp1"
            android:background="@color/color_e1e2e3" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvPopupCancel"
                style="@style/text_10_999"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:padding="@dimen/dp10"
                android:text="取消" />

            <View
                android:layout_width="@dimen/dp1"
                android:layout_height="match_parent"
                android:background="@color/color_e1e2e3" />

            <TextView
                android:id="@+id/tvPopupConfirm"
                style="@style/text_10_blue"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:padding="@dimen/dp10"
                android:text="确认"
                android:textColor="@color/green"
                android:textStyle="bold" />
        </LinearLayout>

    </LinearLayout>

</LinearLayout>


