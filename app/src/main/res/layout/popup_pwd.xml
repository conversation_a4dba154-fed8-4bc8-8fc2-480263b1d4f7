<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="@dimen/dp415"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_green_top_5"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvDialogTitle"
                style="@style/text_14_white"
                android:layout_width="0dp"
                android:layout_marginStart="@dimen/dp10"
                android:layout_weight="1"
                android:text="设置审批密码"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/ivPopupClose"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp5"
                android:padding="@dimen/dp5"
                android:src="@mipmap/ic_close004" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/shape_f7_bottom_5"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp20"
                android:layout_marginTop="@dimen/dp20"
                android:background="@drawable/shape_white_5">

                <TextView
                    android:id="@+id/tvPopupPwd"
                    style="@style/text_12_333"
                    android:maxLength="6"
                    android:paddingVertical="@dimen/dp10"
                    android:paddingStart="@dimen/dp10" />

                <ImageView
                    android:id="@+id/ivCursor"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dp16"
                    android:layout_centerVertical="true"
                    android:layout_toEndOf="@+id/tvPopupPwd"
                    android:src="@drawable/anim_cursor" />

                <TextView
                    android:id="@+id/tvPopupHint"
                    style="@style/text_12_333"
                    android:layout_toEndOf="@+id/ivCursor"
                    android:hint="请输入审批密码"
                    android:maxLength="6"
                    android:paddingVertical="@dimen/dp10" />

            </RelativeLayout>

            <com.yxl.cashier.helper.view.NumberKeyBoardView
                android:id="@+id/numberKeyBoardView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp15"
                android:padding="@dimen/dp4" />

        </LinearLayout>

    </LinearLayout>

    <include
        layout="@layout/layout_toast"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp20" />

</FrameLayout>