<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/linCashierResult"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:visibility="gone">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_white_top_5">

        <ImageView
            android:id="@+id/ivCashierResultBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:padding="@dimen/dp10"
            android:src="@mipmap/ic_back" />

        <TextView
            android:id="@+id/tvCashierResultTitle"
            style="@style/text_16_333"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:text=""
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivCashierResultClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:padding="@dimen/dp10"
            android:src="@mipmap/ic_close001" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/shape_f7_bottom_5"
        android:gravity="center"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/ivCashierResultStatus"
            android:layout_width="@dimen/dp120"
            android:layout_height="@dimen/dp120" />

        <TextView
            android:id="@+id/tvCashierResultStatus"
            style="@style/text_14_999"
            android:text=""
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvCashierResultMoney"
            style="@style/text_14_333"
            android:layout_marginTop="@dimen/dp10"
            android:text="实收金额：￥0.00"
            android:textStyle="bold"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tvCashierResultDisCount"
            style="@style/text_12_red"
            android:layout_marginTop="@dimen/dp5"
            android:text="优惠金额："
            android:visibility="gone" />

        <TextView
            android:id="@+id/tvCashierResultTips"
            style="@style/text_12_999"
            android:layout_marginTop="@dimen/dp10"
            android:gravity="center"
            android:text=""
            android:visibility="gone" />

        <TextView
            android:id="@+id/tvCashierResultDownTimer"
            style="@style/text_14_red"
            android:layout_marginTop="@dimen/dp10"
            android:text="5秒后关闭窗口"
            android:visibility="gone" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp25"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvCashierResultCancel"
                style="@style/text_14_green"
                android:layout_width="@dimen/dp89"
                android:layout_height="@dimen/dp30"
                android:layout_marginHorizontal="@dimen/dp8"
                android:background="@drawable/shape_green_tm_5"
                android:gravity="center"
                android:text="取消收银"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tvCashierResultClose"
                style="@style/text_14_red"
                android:layout_width="@dimen/dp89"
                android:layout_height="@dimen/dp30"
                android:layout_marginHorizontal="@dimen/dp8"
                android:background="@drawable/shape_red_kuang_5"
                android:gravity="center"
                android:text="关闭窗口"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tvCashierResultBack"
                style="@style/text_14_white"
                android:layout_width="@dimen/dp89"
                android:layout_height="@dimen/dp30"
                android:layout_marginHorizontal="@dimen/dp8"
                android:background="@drawable/shape_green_5"
                android:gravity="center"
                android:text="返回收银台"
                android:visibility="gone" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>