<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent"
    android:gravity="center"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="@dimen/dp375"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_white_5"
        android:orientation="vertical"
        android:padding="@dimen/dp20">

        <androidx.core.widget.ContentLoadingProgressBar
            android:id="@+id/pb"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp15"
            android:max="100"
            android:progress="0"
            android:progressDrawable="@drawable/progressbar_horizontal" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp5"
            android:orientation="horizontal">

            <TextView
                style="@style/text_14_blue"
                android:text="@string/versionchecklib_downloading" />

            <TextView
                android:id="@+id/tvProgress"
                style="@style/text_14_blue"
                android:layout_alignParentEnd="true" />
        </RelativeLayout>

    </LinearLayout>

</LinearLayout>