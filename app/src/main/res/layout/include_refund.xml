<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/linRefund"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:visibility="gone">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_white_top_right_5"
        android:gravity="center"
        android:orientation="horizontal">

        <TextView
            style="@style/text_16_333"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:padding="@dimen/dp10"
            android:text="退款"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivRefundClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp10"
            android:src="@mipmap/ic_close001" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp45"
        android:layout_marginTop="@dimen/dp15"
        android:background="@drawable/shape_white_5"
        android:gravity="center"
        android:orientation="horizontal">

        <TextView
            style="@style/text_14_666"
            android:layout_marginStart="@dimen/dp10"
            android:text="商户单号" />

        <FrameLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1">

            <EditText
                android:id="@+id/etRefundCode"
                style="@style/text_14_333"
                android:layout_width="match_parent"
                android:background="@drawable/shape_white_5"
                android:cursorVisible="false"
                android:editable="false"
                android:focusable="true"
                android:focusableInTouchMode="false"
                android:gravity="right"
                android:hint="请扫描/输入商户单号"
                android:inputType="none"
                android:maxLines="1"
                android:padding="@dimen/dp10" />

            <ImageView
                android:id="@+id/ivRefundCode"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp18"
                android:layout_gravity="center_vertical|right"
                android:layout_marginEnd="@dimen/dp5"
                android:src="@drawable/anim_cursor" />

        </FrameLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:gravity="center"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/ivRefundQuestion"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingVertical="@dimen/dp10"
            android:paddingStart="@dimen/dp10"
            android:paddingEnd="@dimen/dp5"
            android:src="@mipmap/ic_question001" />

        <TextView
            style="@style/text_12_666"
            android:text="可在支付通知中的订单详情查看" />

    </LinearLayout>

    <View
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <com.yxl.cashier.helper.view.NumberKeyBoardView
        android:id="@+id/refundKeyBoardView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginHorizontal="@dimen/dp45"
        app:confirm="1" />

</LinearLayout>