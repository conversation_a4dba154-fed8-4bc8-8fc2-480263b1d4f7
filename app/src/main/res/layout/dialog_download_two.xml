<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent"
    android:gravity="center"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <ImageView
            android:layout_width="@dimen/dp375"
            android:layout_height="@dimen/dp153"
            android:background="@mipmap/ic_update_bg"
            android:scaleType="centerCrop" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="@dimen/dp15">

            <TextView
                android:id="@+id/tv_title"
                style="@style/text_16_333"
                android:text="" />

            <TextView
                android:id="@+id/tv_msg"
                style="@style/text_14_666"
                android:layout_width="match_parent"
                android:layout_marginTop="@dimen/dp10"
                android:text="" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp20"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/versionchecklib_version_dialog_cancel"
                    style="@style/text_14_999"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/dp35"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="1"
                    android:background="@drawable/shape_e1e2e3_kuang_5"
                    android:gravity="center"
                    android:text="取消" />

                <Button
                    android:id="@+id/versionchecklib_version_dialog_commit"
                    style="@style/text_14_white"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/dp35"
                    android:layout_weight="1"
                    android:background="@drawable/shape_red_5"
                    android:gravity="center"
                    android:text="升级"
                    android:textColor="@android:color/white" />

            </LinearLayout>

        </LinearLayout>
    </LinearLayout>

</LinearLayout>