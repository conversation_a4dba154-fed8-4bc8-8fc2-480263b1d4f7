<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="@dimen/dp415"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_green_top_5"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                style="@style/text_14_white"
                android:layout_width="0dp"
                android:layout_marginStart="@dimen/dp10"
                android:layout_weight="1"
                android:text="退款操作"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/ivPopupClose"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp5"
                android:padding="@dimen/dp5"
                android:src="@mipmap/ic_close004" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/shape_f7_bottom_5"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp20"
                android:layout_marginTop="@dimen/dp15"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_12_333"
                    android:text="可退款金额：" />

                <TextView
                    android:id="@+id/tvPopupMoney"
                    style="@style/text_12_green"
                    android:layout_marginEnd="@dimen/dp6"
                    android:text="0.00" />

                <TextView
                    android:id="@+id/tvPopupAll"
                    style="@style/text_12_white"
                    android:background="@drawable/shape_yellow_5"
                    android:paddingHorizontal="@dimen/dp6"
                    android:paddingVertical="@dimen/dp2"
                    android:text="全额退" />

            </LinearLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp20"
                android:layout_marginTop="@dimen/dp10"
                android:background="@drawable/shape_white_5">

                <TextView
                    android:id="@+id/etPopupMoney"
                    style="@style/text_12_333"
                    android:paddingVertical="@dimen/dp10"
                    android:paddingStart="@dimen/dp10" />

                <ImageView
                    android:id="@+id/ivCursor"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dp16"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center_vertical|right"
                    android:layout_toEndOf="@+id/etPopupMoney"
                    android:src="@drawable/anim_cursor" />

                <TextView
                    android:id="@+id/etPopupHint"
                    style="@style/text_12_333"
                    android:layout_toEndOf="@+id/ivCursor"
                    android:hint="请输入退款金额"
                    android:paddingVertical="@dimen/dp10"/>

            </RelativeLayout>

            <com.yxl.cashier.helper.view.NumberKeyBoardView
                android:id="@+id/numberKeyBoardView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp15"
                android:padding="@dimen/dp4" />

        </LinearLayout>

    </LinearLayout>

    <include
        layout="@layout/layout_toast"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp20" />

</FrameLayout>