<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_white_5"
    android:clickable="true"
    android:focusable="true"
    android:gravity="center"
    android:orientation="vertical">

    <View
        android:id="@+id/vTop"
        android:layout_width="0dp"
        android:layout_height="0dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/ivClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="right"
            android:padding="@dimen/dp10"
            android:src="@mipmap/ic_close001" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@mipmap/ic_login_img001" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp20"
            android:layout_marginTop="@dimen/dp25"
            android:background="@drawable/shape_e1e2e3_kuang_5"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp10"
                android:src="@mipmap/ic_mobile001" />

            <EditText
                android:id="@+id/etAccount"
                style="@style/text_14_333"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:background="@null"
                android:hint="请输入手机号"
                android:inputType="phone"
                android:maxLength="11"
                android:padding="@dimen/dp8"
                android:text="" />
            <!--测试账号 *********** 123456-->

            <ImageView
                android:id="@+id/ivAccountClear"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp10"
                android:src="@mipmap/ic_close002"
                android:visibility="gone" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp20"
            android:layout_marginTop="@dimen/dp10"
            android:background="@drawable/shape_e1e2e3_kuang_5"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp10"
                android:src="@mipmap/ic_pwd001" />

            <EditText
                android:id="@+id/etPwd"
                style="@style/text_14_333"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:background="@null"
                android:hint="请输入密码"
                android:inputType="textPassword"
                android:maxLength="16"
                android:padding="@dimen/dp8"
                android:text="" />

            <ImageView
                android:id="@+id/ivPwdClear"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp10"
                android:src="@mipmap/ic_close002"
                android:visibility="gone" />

            <ImageView
                android:id="@+id/ivEye"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp10"
                android:src="@mipmap/ic_eye002" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp15"
            android:layout_marginTop="@dimen/dp10"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/ivAuto"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@drawable/checkbox_style" />

                <TextView
                    style="@style/text_12_666"
                    android:text="自动登录" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp5"
                android:gravity="right|center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/ivRemember"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@drawable/checkbox_style" />

                <TextView
                    style="@style/text_12_666"
                    android:text="记住密码" />

            </LinearLayout>

        </LinearLayout>

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/tvLogin"
            style="@style/text_16_white"
            android:layout_width="match_parent"
            android:layout_marginHorizontal="@dimen/dp20"
            android:layout_marginTop="@dimen/dp10"
            android:background="@drawable/shape_green_tm_5"
            android:gravity="center"
            android:paddingVertical="@dimen/dp8"
            android:text="登录"
            android:textStyle="bold" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginVertical="@dimen/dp10"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                style="@style/text_10_999"
                android:text="登录即表示同意" />

            <TextView
                android:id="@+id/tvPrivacy"
                style="@style/text_10_blue"
                android:paddingVertical="@dimen/dp10"
                android:text="《金圈收银插件使用协议》" />

        </LinearLayout>

        <TextView
            android:id="@+id/tvVersion"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp10"
            android:text="测试更新"
            android:textSize="@dimen/f32"
            android:visibility="gone" />

    </LinearLayout>

    <include layout="@layout/layout_toast" />

    <include layout="@layout/include_loading" />

</FrameLayout>