package com.yxl.commonlibrary.base;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.graphics.Bitmap;
import android.media.projection.MediaProjectionManager;
import android.os.Bundle;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.google.gson.Gson;
import com.yxl.commonlibrary.AppManager;
import com.yxl.commonlibrary.Constants;
import com.yxl.commonlibrary.bean.UserInfoData;
import com.yxl.commonlibrary.http.RxHttpManager;
import com.yxl.commonlibrary.utils.SpUtils;

/**
 * BaseApplication
 */
public class BaseApplication extends Application {
    private static BaseApplication sInstance;
    public static Bitmap mBitmap;
    public static String login_address = "com.yxl.cashier.helper.LoginActivity";
    public static boolean isAuto = true;//是否自动登录

    @Override
    public void onCreate() {
        super.onCreate();
        //登录用户信息
        getUserInfo();
        RxHttpManager.init(this);
    }

    /**
     * 当宿主没有继承自该Application时,可以使用set方法进行初始化baseApplication
     */
    public void setApplication(@NonNull BaseApplication application) {
        sInstance = application;
        application.registerActivityLifecycleCallbacks(new ActivityLifecycleCallbacks() {
            @Override
            public void onActivityCreated(@NonNull Activity activity,
                                          @Nullable Bundle savedInstanceState) {
                AppManager.getInstance().addActivity(activity);
            }

            @Override
            public void onActivityStarted(@NonNull Activity activity) {

            }

            @Override
            public void onActivityResumed(@NonNull Activity activity) {

            }

            @Override
            public void onActivityPaused(@NonNull Activity activity) {

            }

            @Override
            public void onActivityStopped(@NonNull Activity activity) {

            }

            @Override
            public void onActivitySaveInstanceState(
                    @NonNull Activity activity, @NonNull Bundle outState) {

            }

            @Override
            public void onActivityDestroyed(@NonNull Activity activity) {
                AppManager.getInstance().removeActivity(activity);
            }
        });

    }


    /**
     * 获得当前app运行的Application
     */
    public static BaseApplication getInstance() {
        if (sInstance == null) {
            throw new NullPointerException("please inherit BaseApplication or call setApplication.");
        }
        return sInstance;
    }

    /***********************Service-start***************************/
    private int result;
    private Intent intent;
    private MediaProjectionManager mMediaProjectionManager;

    public int getResult() {
        return result;
    }

    public Intent getIntent() {
        return intent;
    }

    public MediaProjectionManager getMediaProjectionManager() {
        return mMediaProjectionManager;
    }

    public void setResult(int result1) {
        this.result = result1;
    }

    public void setIntent(Intent intent1) {
        this.intent = intent1;
    }

    public void setMediaProjectionManager(MediaProjectionManager mMediaProjectionManager) {
        this.mMediaProjectionManager = mMediaProjectionManager;
    }

    /***********************Service-end***************************/

    /****************************本地缓存start**************************/

    private UserInfoData userInfoData = null;
    private String strUserInfo;

    /**
     * 保存用户信息
     *
     * @param str
     */
    public void saveUserInfo(String str) {
        SharedPreferences.Editor editor = getSharedPreferences("userinfo", Context.MODE_PRIVATE).edit();
        editor.putString("userinfo", str);
        editor.commit();
        getUserInfo();
    }

    /**
     * 获取本地缓存的用户信息
     */
    public void getUserInfo() {
        SharedPreferences preferences = getSharedPreferences("userinfo", Context.MODE_PRIVATE);
        strUserInfo = preferences.getString("userinfo", null);
        if (TextUtils.isEmpty(strUserInfo)) {
            userInfoData = null;
        } else {
            //解析缓存的str
            userInfoData = new Gson().fromJson(strUserInfo, UserInfoData.class);
        }
    }

    public UserInfoData.DataBean getUserInfoData() {
        if (userInfoData != null) {
            return userInfoData.getData();
        } else {
            return null;
        }
    }

    public String getStrUserInfo() {
        return strUserInfo;
    }


    /**
     * 登录token
     *
     * @return
     */
    public String getToken() {
        if (getUserInfoData() == null) {
            return "";
        } else {
            return getUserInfoData().getToken();
        }
    }

    /**
     * 是否自动登录
     *
     * @return
     */
    public boolean isAuto() {
        return !TextUtils.isEmpty(String.valueOf(SpUtils.getInstance().get(Constants.auto, "")));
    }

    /**
     * 是否记住密码
     *
     * @return
     */
    public boolean isRemember() {
        return !TextUtils.isEmpty(String.valueOf(SpUtils.getInstance().get(Constants.remember, "")));
    }

    /**
     * 登录账号
     *
     * @return
     */
    public String getAccount() {
        return String.valueOf(SpUtils.getInstance().get(Constants.account, ""));
    }

    /**
     * 登录密码
     *
     * @return
     */
    public String getPwd() {
        return String.valueOf(SpUtils.getInstance().get(Constants.pwd, ""));
    }

    /**
     * 开机启动
     *
     * @return
     */
    public boolean isStartUp() {
        return !TextUtils.isEmpty(String.valueOf(SpUtils.getInstance().get(Constants.startUp, "")));
    }

    /**
     * 结算完成最小化
     *
     * @return
     */
    public boolean isPayFinish() {
        return !TextUtils.isEmpty(String.valueOf(SpUtils.getInstance().get(Constants.payFinish, "")));
    }

    /**
     * 退款完成最小化
     *
     * @return
     */
    public boolean isRefundFinish() {
        return !TextUtils.isEmpty(String.valueOf(SpUtils.getInstance().get(Constants.refundFinish, "")));
    }

    /**
     * 退款审核
     *
     * @return
     */
    public boolean isProcess() {
        return !TextUtils.isEmpty(String.valueOf(SpUtils.getInstance().get(Constants.process, "")));
    }

    /**
     * 是否获取焦点
     *
     * @return
     */
    public boolean isFocus() {
        return !TextUtils.isEmpty(String.valueOf(SpUtils.getInstance().get(Constants.isFocus, "")));
    }

    /****************************本地缓存end**************************/

}
