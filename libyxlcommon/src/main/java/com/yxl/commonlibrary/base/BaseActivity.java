package com.yxl.commonlibrary.base;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.media.projection.MediaProjectionManager;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;

import androidx.annotation.LayoutRes;
import androidx.appcompat.app.AppCompatActivity;

import com.gsls.gt.GT;
import com.yxl.commonlibrary.Constants;
import com.yxl.commonlibrary.LoadingDialog;
import com.yxl.commonlibrary.R;
import com.yxl.commonlibrary.utils.KeyBoardUtils;
import com.yxl.commonlibrary.utils.ToastUtils;

import butterknife.ButterKnife;

/**
 * Describe:activity（基类）
 * Created by jingang on 2023/4/24
 */
public abstract class BaseActivity extends AppCompatActivity {
    public BaseActivity TAG = BaseActivity.this;
    private LoadingDialog dialog;
    public int page = 1;
    public String tag;

    @LayoutRes
    protected abstract int getLayoutId();

    public abstract void initViews();

    public void initData() {

    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getLayoutId() != 0) {
            setContentView(getLayoutId());
            ButterKnife.bind(this);
            setStatusBar(true);
        }
        tag = getResources().getString(R.string.app_name);
        mMediaProjectionManager = (MediaProjectionManager) getApplication().getSystemService(Context.MEDIA_PROJECTION_SERVICE);
        initViews();
        initData();
    }

    /**
     * 设置状态栏
     *
     * @param isDark true 字体颜色为黑色，false为白色
     */
    public void setStatusBar(boolean isDark) {
//        StatusBarUtil.transparencyBar(this);
//        StatusBarUtil.setLightStatusBar(this, isDark, true);
//        getWindow().setNavigationBarColor(Color.parseColor("#ffffff"));

        // 全屏显示，隐藏状态栏和导航栏，拉出状态栏和导航栏显示一会儿后消失。
        getWindow().getDecorView().setSystemUiVisibility(
                View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                        | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                        | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                        | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                        | View.SYSTEM_UI_FLAG_FULLSCREEN
                        | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY);

        // 请求全屏模式
//        requestWindowFeature(Window.FEATURE_NO_TITLE);
//        getWindow().setFlags(
//                WindowManager.LayoutParams.FLAG_FULLSCREEN,
//                WindowManager.LayoutParams.FLAG_FULLSCREEN
//        );

//        // 使用沉浸式模式隐藏状态栏和导航栏
//        View decorView = getWindow().getDecorView();
//        decorView.setSystemUiVisibility(
//                View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
//                        | View.SYSTEM_UI_FLAG_LAYOUT_STABLE
//                        | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
//                        | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
//                        | View.SYSTEM_UI_FLAG_FULLSCREEN
//        );

    }

    /**
     * 接口请求等待框
     */
    public void showDialog() {
        if (dialog == null) {
            LoadingDialog.Builder loadBuilder = new LoadingDialog.Builder(this)
                    .setMessage("加载中...")
                    .setCancelable(true)
                    .setCancelOutside(true);
            dialog = loadBuilder.create();
        }
        dialog.showDialog();
    }

    public void hideDialog() {
        if (dialog != null) {
            dialog.hideDialog();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }

    @Override
    public void onBackPressed() {
        if (getCurrentFocus() instanceof EditText) {
            KeyBoardUtils.closeKeyboard((EditText) getCurrentFocus());
        }
        finish();
    }

    /**
     * toast提示
     *
     * @param message
     */
    public void showMessage(String message) {
        ToastUtils.getInstance(this).showMessage(message);
    }

    /**
     * 跳转到指定的activity
     *
     * @param clazz 目标activity
     */
    public void goToActivity(Class clazz) {
        Intent intent = new Intent(this, clazz);
        startActivity(intent);
    }

    /**
     * 跳转到制定activity（带result）
     *
     * @param clazz
     * @param code
     */
    public void goToActivityForResult(Class clazz, int code) {
        Intent intent = new Intent(this, clazz);
        startActivityForResult(intent, code);
    }

    private long currentTime;

    /**
     * 防止快速点击引起ThreadPoolExecutor频繁创建销毁引起crash
     *
     * @return
     */
    public boolean isQuicklyClick() {
        boolean result = false;
        if (System.currentTimeMillis() - currentTime <= 1000) {
            result = true;
            Log.e("111111", "is too quickly!");
        }
        currentTime = System.currentTimeMillis();
        return result;
    }

    /**
     * 隐藏软键盘
     *
     * @param activity 当前Activity
     */
    public void hideSoftInput(Activity activity) {
        InputMethodManager inputMethodManager =
                (InputMethodManager) activity.getSystemService(Context.INPUT_METHOD_SERVICE);
        inputMethodManager.hideSoftInputFromWindow(
                activity.getWindow().getDecorView().getWindowToken(), 0);
    }


    /***********************Service-start***************************/
    //截屏
    private int result = 0;
    private Intent intent = null;
    private MediaProjectionManager mMediaProjectionManager;
    private Class<?> cls;

    /**
     * 启动服务
     */
    public void startIntent(Class<?> cls) {
        this.cls = cls;
        if (intent != null && result != 0) {
            ((BaseApplication) getApplication()).setResult(result);
            ((BaseApplication) getApplication()).setIntent(intent);
            GT.startFloatingWindow(this, cls);
            finish();
        } else {
            startActivityForResult(mMediaProjectionManager.createScreenCaptureIntent(), Constants.PERMISSION_PROJECTION);
            ((BaseApplication) getApplication()).setMediaProjectionManager(mMediaProjectionManager);
        }
    }

    @SuppressLint("MissingSuperCall")
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        switch (requestCode) {
            case Constants.PERMISSION_PROJECTION:
                if (resultCode == RESULT_OK) {
                    result = resultCode;
                    intent = data;
                    ((BaseApplication) getApplication()).setResult(resultCode);
                    ((BaseApplication) getApplication()).setIntent(data);
                    GT.startFloatingWindow(this, cls);
                    finish();
                    break;
                } else {
                    finish();
                }
        }
    }

    /***********************Service-end***************************/


    /****************************本地缓存start**************************/

    public String getToken() {
        return BaseApplication.getInstance().getToken();
    }

    /**
     * 是否自动登录
     *
     * @return
     */
    public boolean isAuto() {
        return BaseApplication.getInstance().isAuto();
    }

    /**
     * 是否记住密码
     *
     * @return
     */
    public boolean isRemember() {
        return BaseApplication.getInstance().isRemember();
    }

    /**
     * 登录账号
     *
     * @return
     */
    public String getAccount() {
        return BaseApplication.getInstance().getAccount();
    }

    /**
     * 登录密码
     *
     * @return
     */
    public String getPwd() {
        return BaseApplication.getInstance().getPwd();
    }

    /**
     * 开机启动
     *
     * @return
     */
    public boolean isStartUp() {
        return BaseApplication.getInstance().isStartUp();
    }

    /**
     * 结算完成最小化
     *
     * @return
     */
    public boolean isPayFinish() {
        return BaseApplication.getInstance().isPayFinish();
    }

    /**
     * 退款完成最小化
     *
     * @return
     */
    public boolean isRefundFinish() {
        return BaseApplication.getInstance().isRefundFinish();
    }

    /**
     * 退款审核
     *
     * @return
     */
    public boolean isProcess() {
        return BaseApplication.getInstance().isProcess();
    }

    /****************************本地缓存end**************************/

}
