package com.yxl.commonlibrary.base;

/**
 * Describe:
 * Created by jingang on 2023/2/4
 */
public class BaseData {
    /**
     * code : 200
     * msg : 操作成功
     * data : {"shopUnique":1536215939565,"staffId":3586,"staffPhone":"158****5898","staffName":"益农社服务员","staffPosition":3,"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjM1ODYsInJuU3RyIjoiS3lCZDRycGlsbXBFanpYTG1tdHdFT2ZnZlBxOGluWU4ifQ.7LAR7e5gzPbC0RswKTHyCctcYHg1mJEwwcPcnwtpGoQ"}
     */

    private int code;
    private String msg;
    private Object data;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }
}
