package com.yxl.commonlibrary.base;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;

import androidx.annotation.LayoutRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.yxl.commonlibrary.LoadingDialog;
import com.yxl.commonlibrary.R;
import com.yxl.commonlibrary.utils.ToastUtils;

import butterknife.ButterKnife;
import butterknife.Unbinder;

/**
 * fragment基类
 * Created by jingang on 2023/01/04.
 */
public abstract class BaseFragment extends Fragment {
    public String tag;
    Unbinder unbinder;
    public View contentView;

    public int page = 1;
    private LoadingDialog dialog;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        if (contentView == null) {
            if (getLayoutId() == 0) {
                return contentView;
            } else {
                contentView = inflater.inflate(getLayoutId(), container, false);
            }
        } else {
            ViewGroup viewGroup = (ViewGroup) contentView.getParent();
            if (viewGroup != null) {
                viewGroup.removeView(contentView);
            }
        }

        if (contentView == null) {
            return null;
        }
        unbinder = ButterKnife.bind(this, contentView);
        return contentView;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initView(savedInstanceState);
        tag = getResources().getString(R.string.app_name);
        initData();
    }

    protected abstract @LayoutRes
    int getLayoutId();

    /**
     * 初始化控件
     */
    protected abstract void initView(@Nullable Bundle savedInstanceState);

    public void initData() {

    }

    /**
     * toast提示
     *
     * @param message
     */
    public void showMessage(String message) {
        ToastUtils.getInstance(getActivity()).showMessage(message);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (unbinder != null) {
            unbinder.unbind();
        }
    }

    public void showDialog() {
        // 隐藏状态栏
        Window window = getActivity().getWindow();
        window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        window.addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN);
        if (dialog == null) {
            LoadingDialog.Builder loadBuilder = new LoadingDialog.Builder(getActivity())
                    .setMessage("加载中...")
                    .setCancelable(true)
                    .setCancelOutside(true);
            dialog = loadBuilder.create();
        }
        dialog.showDialog();
        //隐藏导航栏
        window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_HIDE_NAVIGATION);
        window.getDecorView().setOnSystemUiVisibilityChangeListener(visibility -> {
            int uiOptions = View.SYSTEM_UI_FLAG_LAYOUT_STABLE |
                    //布局位于状态栏下方
                    View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION |
                    //全屏
                    View.SYSTEM_UI_FLAG_FULLSCREEN |
                    //隐藏导航栏
                    View.SYSTEM_UI_FLAG_HIDE_NAVIGATION |
                    View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN;
            uiOptions |= View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY;
            window.getDecorView().setSystemUiVisibility(uiOptions);
        });
    }

    public void hideDialog() {
        if (dialog != null) {
            dialog.hideDialog();
        }
    }

    /**
     * 跳转到指定的activity
     *
     * @param clazz 目标activity
     */
    public void goToActivity(Class clazz) {
        Intent intent = new Intent(getActivity(), clazz);
        startActivity(intent);
    }

    /**
     * 跳转到制定activity（带result）
     *
     * @param clazz
     * @param code
     */
    public void goToActivityForResult(Class clazz, int code) {
        Intent intent = new Intent(getActivity(), clazz);
        startActivityForResult(intent, code);
    }

    private long currentTime;

    /**
     * 防止快速点击引起ThreadPoolExecutor频繁创建销毁引起crash
     *
     * @return
     */
    public boolean isQuicklyClick() {
        boolean result = false;
        if (System.currentTimeMillis() - currentTime <= 1000) {
            result = true;
            Log.e(tag, "is too quickly!");
        }
        currentTime = System.currentTimeMillis();
        return result;
    }

    /**
     * 隐藏软键盘
     *
     * @param activity 当前Activity
     */
    public void hideSoftInput(Activity activity) {
        InputMethodManager inputMethodManager = (InputMethodManager) activity.getSystemService(Context.INPUT_METHOD_SERVICE);
        inputMethodManager.hideSoftInputFromWindow(activity.getWindow().getDecorView().getWindowToken(), 0);
    }

    /****************************本地缓存start**************************/

    public String getToken() {
        return BaseApplication.getInstance().getToken();
    }

    /**
     * 是否自动登录
     *
     * @return
     */
    public boolean isAuto() {
        return BaseApplication.getInstance().isAuto();
    }

    /**
     * 是否记住密码
     *
     * @return
     */
    public boolean isRemember() {
        return BaseApplication.getInstance().isRemember();
    }

    /**
     * 登录账号
     *
     * @return
     */
    public String getAccount() {
        return BaseApplication.getInstance().getAccount();
    }

    /**
     * 登录密码
     *
     * @return
     */
    public String getPwd() {
        return BaseApplication.getInstance().getPwd();
    }

    /**
     * 开机启动
     *
     * @return
     */
    public boolean isStartUp() {
        return BaseApplication.getInstance().isStartUp();
    }

    /**
     * 结算完成最小化
     *
     * @return
     */
    public boolean isPayFinish() {
        return BaseApplication.getInstance().isPayFinish();
    }

    /**
     * 退款完成最小化
     *
     * @return
     */
    public boolean isRefundFinish() {
        return BaseApplication.getInstance().isRefundFinish();
    }

    /**
     * 退款审核
     *
     * @return
     */
    public boolean isProcess() {
        return BaseApplication.getInstance().isProcess();
    }

    /****************************本地缓存end**************************/

}