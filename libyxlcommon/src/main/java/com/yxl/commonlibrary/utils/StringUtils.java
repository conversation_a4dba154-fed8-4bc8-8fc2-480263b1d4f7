package com.yxl.commonlibrary.utils;

import android.text.TextUtils;

import com.yxl.commonlibrary.base.BaseApplication;

import java.math.BigDecimal;
import java.util.Random;

public class StringUtils {

    /**
     * 手机号中间四位***
     *
     * @param mobile
     * @return
     */
    public static String getStarMobile(String mobile) {
        if (!TextUtils.isEmpty(mobile)) {
            if (mobile.length() >= 11)
                return mobile.substring(0, 3) + "****" + mobile.substring(7);
        } else {
            return "";
        }
        return mobile;
    }

    /**
     * 重组图片地址
     * 1.判断非空 2.转换斜杠 3.判断是否含有"http"
     *
     * @param url
     * @return
     */
    public static String handledImgUrl(String url) {
        if (url == null) return "";
        url = url.replace("\\", "/");
        if (url.contains("http")) return url;
        return "https://file.buyhoo.cc/" + url;
    }

    /**
     * 生成订单号
     *
     * @return
     */
    public static String getOrderId() {
        // 获取当前时间的毫秒数
        long timeList = System.currentTimeMillis();

        // 获取当前时间的后两位数字
        String accountNumTmp = String.valueOf(BaseApplication.getInstance().getUserInfoData().getStaffId()); // 请替换为实际的账号数字
        String accountNumLastTwoDigits = accountNumTmp.substring(Math.max(0, accountNumTmp.length() - 2));

        // 生成一个随机数字（0到9之间）
        Random random = new Random();
        int srandNum = random.nextInt(10);

        // 构建订单号
        return timeList + srandNum + accountNumLastTwoDigits;
    }

    /**
     * 判断一个字符串是否包含空格，并截取空格前的子串
     *
     * @param input
     * @return
     */
    public static String getSubstringBeforeSpace(String input) {
        if (TextUtils.isEmpty(input)) {
            return "";
        } else {
            // 判断字符串是否包含空格
            boolean hasSpace = input.contains(" ");
            if (hasSpace) {
                // 找到第一个空格的位置
                int spaceIndex = input.indexOf(' ');
                // 截取空格前的部分
                return input.substring(0, spaceIndex);
            } else {
                // 如果没有空格，则返回原字符串
                return input;
            }
        }

    }

    /**
     * 判断一个字符串是否包含空格，并截取空格前的子串
     *
     * @param input
     * @return
     */
    public static String getHandledOcrMoney(String input) {
        if (TextUtils.isEmpty(input)) {
            return "";
        } else {
            StringBuilder result = new StringBuilder();
            boolean foundFirstDigit = false;
            for (char c : input.toCharArray()) {
                if (Character.isDigit(c)) {
                    if (!foundFirstDigit) {
                        foundFirstDigit = true;
                    }
                    result.append(c);
                } else if (foundFirstDigit) {
                    result.append(c);
                }
            }
            input = result.toString();
            // 判断字符串是否包含空格
            boolean hasSpace = input.contains(" ");
            if (hasSpace) {
                // 找到第一个空格的位置
                int spaceIndex = input.indexOf(' ');
                input = input.substring(0, spaceIndex);
                // 正则表达式匹配非数字和非小数点的字符
                String regex = "[^\\d.]";
                input = input.replaceAll(regex, "");
                boolean hasDot = input.contains(".");
                if (hasDot){
                    int dotIndex = input.indexOf('.');
                    input = input.substring(0, input.length()-dotIndex > 2 ? dotIndex+3:input.length());
                }

                return input;
            } else {
                // 如果没有空格，则返回原字符串
                String regex = "[^\\d.]";
                input = input.replaceAll(regex, "");
                boolean hasDot = input.contains(".");
                if (hasDot){
                    int dotIndex = input.indexOf('.');
                    input = input.substring(0, input.length()-dotIndex > 2 ? dotIndex+3:input.length());
                }
                return input;
            }
        }

    }
}
