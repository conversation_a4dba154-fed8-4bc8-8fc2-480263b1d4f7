package com.yxl.commonlibrary.utils;

import java.math.RoundingMode;
import java.text.DecimalFormat;

/**
 * Descride:小数点后面取两位小数并四舍五入
 * Created by jingang on 2021/2/27
 */
public class DFUtils {

    public static String getNum(double num) {
        DecimalFormat decimalFormat = new DecimalFormat("0");
        decimalFormat.setRoundingMode(RoundingMode.DOWN);//舍入模式，其中的值被舍入为零。
        return decimalFormat.format(num);
    }

    //保留double类型小数后两位，不四舍五入，直接取小数后两位 比如：10.1269 返回：10.12
    public static String getNum2(double num) {
        DecimalFormat decimalFormat = new DecimalFormat("0.00");
        decimalFormat.setRoundingMode(RoundingMode.DOWN);//舍入模式，其中的值被舍入为零。
        return decimalFormat.format(num);
    }

    /**
     * 去掉多余的.与0
     *
     * @param num
     * @return
     */
    public static String getNum4(double num) {
        String s = getNum2(num);
        if (s.indexOf(".") > 0) {
            s = s.replaceAll("0+?$", "");//去掉多余的0
            s = s.replaceAll("[.]$", "");//如最后一位是.则去掉
        }
        return s;
    }
}
