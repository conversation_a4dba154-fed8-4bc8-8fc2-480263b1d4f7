package com.yxl.commonlibrary.utils;

import android.content.Context;
import android.widget.Toast;

/**
 * Describe:Toast公用类
 * Created by jingang on 2022/11/30
 */
public class ToastUtils {

    protected static Toast toast = null;

    private static volatile ToastUtils mToastUtils;

    private ToastUtils(Context context) {
        toast = Toast.makeText(context.getApplicationContext(), "", Toast.LENGTH_SHORT);
    }

    public static ToastUtils getInstance(Context context) {
//        if (null == mToastUtils) {
//            synchronized (ToastUtils.class) {
//                if (null == mToastUtils) {
//                    mToastUtils = new ToastUtils(context);
//                }
//            }
//        }
//        return mToastUtils;
        mToastUtils = new ToastUtils(context);
        return mToastUtils;
    }

    public void showMessage(String toastMsg) {
        toast.setText(toastMsg);
        toast.show();
    }

    public void toastCancel() {
        if (null != toast) {
            toast.cancel();
            toast = null;
        }
        mToastUtils = null;
    }

}
