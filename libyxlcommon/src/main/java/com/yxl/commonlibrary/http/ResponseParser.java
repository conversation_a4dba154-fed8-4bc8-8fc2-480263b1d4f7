package com.yxl.commonlibrary.http;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Looper;
import android.widget.Toast;

import com.gsls.gt.GT;
import com.yxl.commonlibrary.AppManager;
import com.yxl.commonlibrary.Constants;
import com.yxl.commonlibrary.base.BaseApplication;

import org.jetbrains.annotations.NotNull;

import java.io.IOException;
import java.lang.reflect.Type;
import java.util.List;

import okhttp3.Response;
import rxhttp.wrapper.annotation.Parser;
import rxhttp.wrapper.exception.ParseException;
import rxhttp.wrapper.parse.TypeParser;
import rxhttp.wrapper.utils.Converter;

@Parser(name = "Response", wrappers = {List.class})
public class ResponseParser<T> extends TypeParser<T> {
    protected ResponseParser() {
        super();
    }

    public ResponseParser(@NotNull Type type) {
        super(type);
    }

    @Override
    public T onParse(@NotNull Response response) throws IOException {
        BaseResponse<T> data = Converter.convertTo(response, BaseResponse.class, types);

        T t = data.getData(); //获取data字段
        if (t == null && types[0] == String.class) {
            /*
             * 考虑到有些时候服务端会返回：{"errorCode":0,"errorMsg":"关注成功"}  类似没有data的数据
             * 此时code正确，但是data字段为空，直接返回data的话，会报空指针错误，
             * 所以，判断泛型为String类型时，重新赋值，并确保赋值不为null
             */
            t = (T) data.getMsg();
        }
        if (data.getCode() == Constants.SUCCESS_CODE) {
            return t;
        }else{
            throw new ParseException(String.valueOf(data.getCode()), data.getMsg(), response);
        }
//        else if (data.getCode() == 401) {
//            //登录未授权或已过期
//            Looper.prepare();
//            Activity curActivity = AppManager.getInstance().currentActivity();
//            Toast.makeText(curActivity, data.getMsg(), Toast.LENGTH_SHORT).show();
//            BaseApplication.getInstance().saveUserInfo("");
//            SharedPreferences.Editor editor = curActivity.getSharedPreferences("shop", Context.MODE_PRIVATE).edit();
//            editor.clear();
//            editor.commit();
//            curActivity.startActivity(new Intent(BaseApplication.login_address));
//            AppManager.getInstance().finishAllActivity();
//            Looper.loop();
//            return null;
//            return (T) String.valueOf(data.getCode());
//            throw new ParseException(String.valueOf(data.getCode()), data.getMsg(), response);
//        } else {
//            throw new ParseException(String.valueOf(data.getCode()), data.getMsg(), response);
//        }
    }
}
