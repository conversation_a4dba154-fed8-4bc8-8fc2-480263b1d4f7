package com.yxl.commonlibrary.http;

import android.annotation.SuppressLint;
import android.content.Context;
import android.net.Uri;
import android.os.Build;
import android.text.TextUtils;
import android.util.Log;

import androidx.lifecycle.LifecycleOwner;

import com.rxjava.rxlife.RxLife;
import com.yxl.commonlibrary.base.BaseApplication;
import com.yxl.commonlibrary.utils.PackageUtils;
import com.yxl.commonlibrary.utils.SystemUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import rxhttp.RxHttp;

/**
 * 接口请求方法类
 */
public class RXHttpUtil {
    private static String tag = "RXHttpUtil";

    /**
     * 商品修改记录信息
     *
     * @return
     */
    private static Map<String, String> getHeader() {
        Map<String, String> map = new HashMap<>();
        return map;
    }

    /**
     * 转码
     *
     * @param url
     * @return
     */
    private static String navticeEncode(String url) {
        String resulUrl = "";
        try {
            resulUrl = URLEncoder.encode(url, "UTF-8");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resulUrl;
    }

    /**
     * 请求方式：get
     * 返回：String
     */
    public static <T> void requestByGetAsResponseString(LifecycleOwner owner, String path, Map<String, Object> params, RequestListener<String> requestListener) {
        RxHttp.get(path)
                .addHeader("Authorization", "Bearer " + getToken())
                .addAll(params)
                .asString()
                .observeOn(AndroidSchedulers.mainThread())
                .to(RxLife.as(owner))
                .subscribe(s -> {
                            if (requestListener != null) {
                                requestListener.success(s);
                            }
                        }
                        , throwable -> {
                            if (requestListener != null) {
                                requestListener.onError(throwable.getMessage(),throwable.getLocalizedMessage());
                            }
                            postDingDing(owner, path, params, throwable);
                        });
    }

    /**
     * 请求方式：get
     * 传参方式：form
     * 返回：data
     */
    public static <T> void requestByGetAsResponse(LifecycleOwner owner, String path, Map<String, Object> params, Class<T> t, RequestListener<T> requestListener) {
        RxHttp.get(path)
                .addHeader("Authorization", "Bearer " + getToken())
                .addAll(params)
                .asResponse(t)
                .observeOn(AndroidSchedulers.mainThread())
                .to(RxLife.as(owner))
                .subscribe(t1 -> {
                            if (requestListener != null) {
                                requestListener.success(t1);
                            }
                        }
                        , throwable -> {
                            Log.e(tag,"code = "+throwable.getLocalizedMessage()+" "+throwable.getSuppressed()+" "+throwable.getStackTrace());
                            if (requestListener != null) {
                                requestListener.onError(throwable.getMessage(),throwable.getLocalizedMessage());
                            }
                            postDingDing(owner, path, params, throwable);
                        });
    }

    /**
     * 请求方式：get
     * 传参方式：form
     * 返回：list
     */
    public static <T> void requestByGetAsResponseList(LifecycleOwner owner, String path, Map<String, Object> params, Class<T> t, RequestListListener<T> requestListener) {
        RxHttp.GetMyForm(path)
                .addHeader("Authorization", "Bearer " + getToken())
                .addAll(params)
                .asResponseList(t)
                .observeOn(AndroidSchedulers.mainThread())
                .to(RxLife.as(owner))
                .subscribe(tList -> {
                            if (requestListener != null) {
                                requestListener.onResult(tList);
                            }
                        }
                        , throwable -> {
                            if (requestListener != null) {
                                requestListener.onError(throwable.getMessage(),throwable.getLocalizedMessage());
                            }
                            postDingDing(owner, path, params, throwable);
                        });
    }

    /**
     * 请求方式：post
     * 传参方式：body
     * 返回：string
     */
    public static void requestByBodyPostAsOriginalResponse(LifecycleOwner owner, String path, Map<String, Object> params, RequestListener<String> requestListener) {
        RxHttp.postBody(path)
                .addHeader("Authorization", "Bearer " + getToken())
                .setBody(params)
                .asString()
                .observeOn(AndroidSchedulers.mainThread())
                .to(RxLife.as(owner))
                .subscribe(s -> {
                            if (requestListener != null) {
                                requestListener.success(s);
                            }
                        }
                        , throwable -> {
                            if (requestListener != null) {
                                requestListener.onError(throwable.getMessage(),throwable.getLocalizedMessage());
                            }
                            postDingDing(owner, path, params, throwable);
                        });
    }

    /**
     * 请求方式：post
     * 传参方式：body
     * 返回：data
     */
    public static <T> void requestByBodyPostAsResponse(LifecycleOwner owner, String path, Map<String, Object> params, Class<T> t, RequestListener<T> requestListener) {
        RxHttp.postBody(path)
                .addHeader("Authorization", "Bearer " + getToken())
                .setBody(params)
                .asResponse(t)
                .observeOn(AndroidSchedulers.mainThread())
                .to(RxLife.as(owner))
                .subscribe(t1 -> {
                            if (requestListener != null) {
                                requestListener.success(t1);
                            }
                        }
                        , throwable -> {
                            if (requestListener != null) {
                                requestListener.onError(throwable.getMessage(),throwable.getLocalizedMessage());
                            }
                            postDingDing(owner, path, params, throwable);
                        });
    }

    /**
     * 请求方式：post
     * 传参方式：body
     * 返回：list
     */
    public static <T> void requestByBodyPostAsResponseList(LifecycleOwner owner, String path, Map<String, Object> params, Class<T> t, RequestListListener<T> requestListener) {
        RxHttp.postBody(path)
                .addHeader("Authorization", "Bearer " + getToken())
                .setBody(params)
                .asResponseList(t)
                .observeOn(AndroidSchedulers.mainThread())
                .to(RxLife.as(owner))
                .subscribe(tList -> {
                            if (requestListener != null) {
                                requestListener.onResult(tList);
                            }
                        }
                        , throwable -> {
                            if (requestListener != null) {
                                requestListener.onError(throwable.getMessage(),throwable.getLocalizedMessage());
                            }
                            postDingDing(owner, path, params, throwable);
                        });
    }

    /**
     * 上传文件 （单张）
     *
     * @param owner
     * @param path
     * @param file
     * @param t
     * @param requestListener
     * @param <T>
     */
    public static <T> void requestByPostUploadFile(LifecycleOwner owner, String path, File file, Class<T> t, RequestListener<T> requestListener) {
        Map<String, Object> map = new HashMap<>();
        map.put("file", file);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            RxHttp.postForm(path)
                    .addPart((Context) owner, "file", Uri.fromFile(file))
                    .asResponse(t)
                    .observeOn(AndroidSchedulers.mainThread())
                    .to(RxLife.as(owner))
                    .subscribe(t1 -> {
                                if (requestListener != null) {
                                    requestListener.success(t1);
                                }
                            }
                            , throwable -> {
                                if (requestListener != null) {
                                    requestListener.onError(throwable.getMessage(),throwable.getLocalizedMessage());
                                }
                                postDingDing(owner, path, map, throwable);
                            });
        } else {
            RxHttp.postForm(path)
                    .addFile("file", file)
                    .asResponse(t)
                    .observeOn(AndroidSchedulers.mainThread())
                    .to(RxLife.as(owner))
                    .subscribe(t1 -> {
                                if (requestListener != null) {
                                    requestListener.success(t1);
                                }
                            }
                            , throwable -> {
                                if (requestListener != null) {
                                    requestListener.onError(throwable.getMessage(),throwable.getLocalizedMessage());
                                }
                                postDingDing(owner, path, map, throwable);
                            });
        }
    }

    /**
     * 上传文件 （多张）
     *
     * @param owner
     * @param path
     * @param files
     * @param t
     * @param requestListener
     * @param <T>
     */
    public static <T> void requestByPostUploadFileList(LifecycleOwner owner, String path, List<File> files, Class<T> t, RequestListListener<T> requestListener) {
        Map<String, Object> map = new HashMap<>();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            Map<String, Uri> uriMaps = new HashMap<>();
            for (int i = 0; i < files.size(); i++) {
                uriMaps.put("files", Uri.fromFile(files.get(i)));
                map.put("files", Uri.fromFile(files.get(i)));
            }
            RxHttp.postForm(path)
                    .addParts((Context) owner, uriMaps)
                    .asResponseList(t)
                    .observeOn(AndroidSchedulers.mainThread())
                    .to(RxLife.as(owner))
                    .subscribe(t1 -> {
                                if (requestListener != null) {
                                    requestListener.onResult(t1);
                                }
                            }
                            , throwable -> {
                                if (requestListener != null) {
                                    requestListener.onError(throwable.getMessage(),throwable.getLocalizedMessage());
                                }
                                postDingDing(owner, path, map, throwable);
                            });
        } else {
            Map<String, File> fileMaps = new HashMap<>();
            for (int i = 0; i < files.size(); i++) {
                fileMaps.put("files", files.get(i));
                map.put("files", files.get(i));
            }
            RxHttp.postForm(path)
                    .addFiles("files", files)
                    .asResponseList(t)
                    .observeOn(AndroidSchedulers.mainThread())
                    .to(RxLife.as(owner))
                    .subscribe(t1 -> {
                                if (requestListener != null) {
                                    requestListener.onResult(t1);
                                }
                            }
                            , throwable -> {
                                if (requestListener != null) {
                                    requestListener.onError(throwable.getMessage(),throwable.getLocalizedMessage());
                                }
                                postDingDing(owner, path, map, throwable);
                            });
        }
    }

    /**
     * 获取token（餐饮）
     *
     * @return
     */
    public static String getToken() {
        return BaseApplication.getInstance().getToken();
    }

    /**********************************钉钉告警机器人start******************************************/

    //钉钉业务告警Webhook、sign
    public static String dd_webhook = "https://oapi.dingtalk.com/robot/send?access_token=7f20ecd2378b5bf04d5dfc733354faa020af2476af71cd7799249461ded492d7";
    public static String dd_sign = "SEC7f94c99fde3c143402dedc1d48fb7991cc3841f808b3917cf96418293b106079";
//    public static String dd_webhook = "https://oapi.dingtalk.com/robot/send?access_token=185b95ed2a89280a7b519ec51458df812514921f0827d83f91ff50662cee8d9d";
//    public static String dd_sign = "SEC56c188c6cb6ea805dcc1f3e0acb7ac15ebdcf9890a18c79338d032829e0ac21c";

    /**
     * 发送到钉钉告警机器人
     * 请求方式：post
     * 传参方式：body
     *
     * @param owner
     * @param error
     */
    private static void postDingDing(LifecycleOwner owner, String url, Map<String, Object> params, Throwable error) {
//        //只告警正式环境
//        if (BuildConfig.DEBUG) {
//            return;
//        }
        String code = error.getLocalizedMessage();
        if (TextUtils.isEmpty(code)) {
            return;
        }
        if(code.equals("404") || code.equals("500")){
//        if (code.startsWith("50") || code.startsWith("40")) {
            String title = "金圈收银助手APP接口出错：";
            //用户信息
            String userInfo = "";
            if (TextUtils.isEmpty(BaseApplication.getInstance().getStrUserInfo())) {
                userInfo = "无  \n";
            } else {
                JSONObject objectCheck;
                try {
                    objectCheck = new JSONObject(BaseApplication.getInstance().getStrUserInfo());
                    userInfo = String.valueOf(objectCheck.getJSONObject("data"));
                } catch (JSONException e) {
                    e.printStackTrace();
                    userInfo = "无  \n";
                }
            }
            //手机系统信息
            String systemInfo = "androidBuyhoo  \n" +
                    "版本号：" + PackageUtils.getPackageName((Context) owner) + "  \n" +
                    "手机型号：" + SystemUtils.getSystemModel() + "  \n" +
                    "手机系统版本：" + SystemUtils.getSystemVersion();
            //@
            String at = "{\"atMobiles\":[\"17865069350\",\"17611323607\",\"15376087225\",\"13854939193\"],\"atUserIds\":[],\"isAtAll\": false}";

            String text = "## 金圈收银助手APP接口出错：  \n" +
                    "@17865069350 @17611323607 @15376087225 @13854939193  \n" +
                    "**请求url：**  \n[" + url + "](" + url + ")  \n" +
                    "**参数：**  \n" + new JSONObject(params) + "  \n" +
                    "**错误信息：**  \n" + error + "  \n" +
                    "**用户信息：**  \n" + userInfo + "  \n" +
                    "**系统信息：**  \n" + systemInfo;

            Map<String, Object> mapMarkdown = new HashMap<>(),
                    map = new HashMap<>();
            mapMarkdown.put("title", title);
            mapMarkdown.put("text", text);
            map.put("msgtype", "markdown");
            map.put("markdown", mapMarkdown);
            map.put("at", at);
            RxHttp.postBody(dd_webhook + "&timestamp=" + System.currentTimeMillis() + "&sign=" + getDdSign())
                    .setBody(map)
                    .asString()
                    .observeOn(AndroidSchedulers.mainThread())
                    .to(RxLife.as(owner))
                    .subscribe(s -> {
                                Log.e(tag, "发送到钉钉告警机器人成功：" + s);
                            }
                            , throwable -> {
                                Log.e(tag, "发送到钉钉告警机器人失败：" + throwable.getMessage());
                            });
        }
    }

    /**
     * 获取钉钉告警机器人sign
     *
     * @return
     */
    @SuppressLint("NewApi")
    private static String getDdSign() {
        Long timestamp = System.currentTimeMillis();
        String stringToSign = timestamp + "\n" + dd_sign;
        String sign = "";
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(dd_sign.getBytes("UTF-8"), "HmacSHA256"));
            byte[] signData = mac.doFinal(stringToSign.getBytes("UTF-8"));
            sign = URLEncoder.encode(new String(Base64.getEncoder().encode(signData)), "UTF-8");
        } catch (NoSuchAlgorithmException | UnsupportedEncodingException | InvalidKeyException e) {
            e.printStackTrace();
        }
        return sign;
    }

    /**********************************钉钉告警机器人end******************************************/

}
