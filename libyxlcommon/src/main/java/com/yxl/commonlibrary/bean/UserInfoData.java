package com.yxl.commonlibrary.bean;

import java.io.Serializable;

/**
 * Describe:登录用户信息（实体类）
 * Created by jingang on 2024/1/17
 */
public class UserInfoData implements Serializable {

    /**
     * code : 200
     * msg : 操作成功
     * data : {"shopUnique":1536215939565,"staffId":3586,"staffPhone":"158****5898","staffName":"益农社服务员","staffPosition":3,"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjM1ODYsInJuU3RyIjoiS3lCZDRycGlsbXBFanpYTG1tdHdFT2ZnZlBxOGluWU4ifQ.7LAR7e5gzPbC0RswKTHyCctcYHg1mJEwwcPcnwtpGoQ"}
     */

    private int code;
    private String msg;
    private DataBean data;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public DataBean getData() {
        return data;
    }

    public void setData(DataBean data) {
        this.data = data;
    }

    public static class DataBean {
        /**
         * shopUnique : 1536215939565
         * staffId : 3586
         * staffPhone : 158****5898
         * staffName : 益农社服务员
         * staffPosition : 3
         * token : eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjM1ODYsInJuU3RyIjoiS3lCZDRycGlsbXBFanpYTG1tdHdFT2ZnZlBxOGluWU4ifQ.7LAR7e5gzPbC0RswKTHyCctcYHg1mJEwwcPcnwtpGoQ
         */

        private long shopUnique;
        private int staffId;
        private String staffPhone;
        private String staffName;//员工名称
        private int staffPosition;//员工职位
        private String shopName;//商户名称
        private String branchName;//店铺名称
        private String token;

        public long getShopUnique() {
            return shopUnique;
        }

        public void setShopUnique(long shopUnique) {
            this.shopUnique = shopUnique;
        }

        public int getStaffId() {
            return staffId;
        }

        public void setStaffId(int staffId) {
            this.staffId = staffId;
        }

        public String getStaffPhone() {
            return staffPhone;
        }

        public void setStaffPhone(String staffPhone) {
            this.staffPhone = staffPhone;
        }

        public String getStaffName() {
            return staffName;
        }

        public void setStaffName(String staffName) {
            this.staffName = staffName;
        }

        public int getStaffPosition() {
            return staffPosition;
        }

        public void setStaffPosition(int staffPosition) {
            this.staffPosition = staffPosition;
        }

        public String getShopName() {
            return shopName;
        }

        public void setShopName(String shopName) {
            this.shopName = shopName;
        }

        public String getBranchName() {
            return branchName;
        }

        public void setBranchName(String branchName) {
            this.branchName = branchName;
        }

        public String getToken() {
            return token;
        }

        public void setToken(String token) {
            this.token = token;
        }
    }
}
