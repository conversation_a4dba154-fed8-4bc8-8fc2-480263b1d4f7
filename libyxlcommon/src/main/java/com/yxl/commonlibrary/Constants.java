package com.yxl.commonlibrary;

/**
 * Describe:
 * Created by jingang on 2019/6/19
 */
public interface Constants {
    //接口地址
    String Host = "http://app.kzjtv.com/v2/";

    int SUCCESS_CODE = 200;
    int limit = 10;
    int finish_time = 5;//收银完成最小化倒计时(s)
    int cashier_max = 50000;//收银金额最大限制
    double cashier_min = 0.02;//收银金额最小限制

    String CHANNEL_ID = "my_foreground_service_channel";//通知通道名称
    int notification_id = 1;
    String notification_name = "cashier_helper1";

    String isFocus = "isFocus";//是否获取焦点（小窗口使用）
    String token = "token";
    String auto = "auto";//自动登录
    String remember = "remember";//记住密码
    String account = "account";//登录账号
    String pwd = "pwd";//登录密码
    String startUp = "startUp";//开机启动
    String payFinish = "payFinish";//结算完成最小化
    String refundFinish = "refundFinish";//退款完成最小化
    String process = "process";//退款审核
    String LEFT = "left";
    String TOP = "top";
    String RIGHT = "right";
    String BOTTOM = "bottom";
    String CROP_WIDTH = "crop_width";
    String CROP_HEIGHT = "crop_height";
    String BARCODE = "barcode";//扫码枪扫码结果
    String BITMAP = "bitmap";
    String LAST_UPDATE = "last_update";//上次版本更新

    int PERMISSION = 0x01;
    int PERMISSION_FLOAT = 0x03;//悬浮窗
    int PERMISSION_PROJECTION = 0x04;//屏幕捕捉

}
