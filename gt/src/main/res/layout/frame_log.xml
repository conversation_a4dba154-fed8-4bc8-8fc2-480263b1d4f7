<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#60A162"
    >

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="vertical"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_margin="10dp"/>

    <CheckBox
        android:id="@+id/cb_dynamicRefresh"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="#59FFFFFF"
        android:checked="true"
        android:text="动态刷新"
        android:textColor="#92FFFFFF"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <Button
        android:id="@+id/btn_empty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="清空"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        />

</androidx.constraintlayout.widget.ConstraintLayout>