<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cl_close"
    android:layout_width="0dp"
    android:layout_height="0dp"
    android:background="#020202"
    android:visibility="gone"
    app:layout_constraintBottom_toBottomOf="@+id/view_bg"
    app:layout_constraintEnd_toEndOf="@+id/view_bg"
    app:layout_constraintStart_toStartOf="@+id/view_bg"
    app:layout_constraintTop_toTopOf="@+id/view_bg">

    <TextView
        android:id="@+id/tv_shutdown"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="关机"
        android:textColor="#BAFFEB3B"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ProgressBar
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="#36554A4A"
        app:layout_constraintBottom_toTopOf="@+id/tv_shutdown"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.8" />

</androidx.constraintlayout.widget.ConstraintLayout>