<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/ll_bottom"
    android:layout_width="0dp"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_gray_top"
    android:orientation="horizontal"
    android:padding="5dp"
    app:layout_constraintBottom_toBottomOf="@+id/view_bg"
    app:layout_constraintEnd_toEndOf="@+id/view_bg"
    app:layout_constraintStart_toStartOf="@+id/view_bg">

    <TextView
        android:id="@+id/tv_back"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center"
        android:padding="5dp"
        android:text="◀" />

    <TextView
        android:id="@+id/tv_home"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center"
        android:padding="5dp"
        android:text="●" />

    <TextView
        android:id="@+id/tv_task"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center"
        android:padding="5dp"
        android:text="■" />

</LinearLayout>