<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cl_title"
    android:layout_width="0dp"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_gray_bottom"
    android:paddingLeft="10dp"
    android:paddingTop="5dp"
    android:paddingRight="10dp"
    android:paddingBottom="5dp"
    app:layout_constraintEnd_toEndOf="@+id/view_bg"
    app:layout_constraintStart_toStartOf="@+id/view_bg"
    app:layout_constraintTop_toTopOf="@+id/view_bg">

    <SearchView
        android:id="@+id/sv_find"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:queryHint="请输入搜索内容"
        app:layout_constraintEnd_toStartOf="@+id/tv_hide"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_hide"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:padding="8dp"
        android:rotation="0"
        android:rotationX="0"
        android:rotationY="0"
        android:text="▬"
        android:textColor="#FF00"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/cb_expansion"
        app:layout_constraintTop_toTopOf="parent" />

    <CheckBox
        android:id="@+id/cb_expansion"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textColor="#FF00"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/tv_close"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:padding="8dp"
        android:text="✖"
        android:textColor="#FF00"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>