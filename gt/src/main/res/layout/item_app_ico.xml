<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:gravity="center"
    >

    <ImageView
        android:id="@+id/iv_icon"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:src="@drawable/bg_black"
        />

    <view class="com.gsls.gt.GT$ViewUtils$MarqueeTextView"
        android:id="@+id/tv_appName"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="app"
        android:textSize="10sp"
        android:gravity="center"
        />



</LinearLayout>