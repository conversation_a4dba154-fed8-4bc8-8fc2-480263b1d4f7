<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_filtrate"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="RtlSymmetry">

    <View
        android:id="@+id/view_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#70000000" />

    <LinearLayout
        android:id="@+id/ll_bottom1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        android:background="@drawable/round_circle_white2"
        android:orientation="vertical"
        android:paddingBottom="10dp"
        app:layout_constraintBottom_toTopOf="@+id/btn_cancel">


        <TextView
            android:id="@+id/tv_photo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:gravity="center"
            android:padding="10dp"
            android:text="拍摄"
            android:textColor="#202020"
            app:layout_constraintTop_toBottomOf="@+id/view_line3" />

        <View
            android:id="@+id/view_line3"
            android:layout_width="match_parent"
            android:layout_height="2dp"
            android:layout_marginTop="5dp"
            android:background="#F2F2F2"
            app:layout_constraintTop_toBottomOf="@+id/rl2" />

        <TextView
            android:id="@+id/tv_img"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:gravity="center"
            android:padding="10dp"
            android:text="从相册选择"
            android:textColor="#202020"
            app:layout_constraintTop_toBottomOf="@+id/view_line3" />


    </LinearLayout>

    <Button
        android:id="@+id/btn_cancel"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@drawable/round_circle_white7"
        android:gravity="center"
        android:padding="10dp"
        android:text="取消"
        android:textColor="#202020"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/view_bg"
        app:layout_constraintStart_toStartOf="@+id/view_bg" />

</androidx.constraintlayout.widget.ConstraintLayout>