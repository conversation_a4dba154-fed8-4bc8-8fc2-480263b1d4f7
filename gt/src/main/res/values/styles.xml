<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="ResourceName">

    <!-- 全屏对话框 -->
    <style name="Dialog" parent="android:style/Theme.Dialog">
        <item name="android:background">#00000000</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
    </style>

    <!-- 对话框显示退出动画 -->
    <style name="bottomDialogAnimation" parent="android:style/Theme.Dialog">
        <item name="android:windowEnterAnimation">@anim/bottom_in</item>
        <item name="android:windowExitAnimation">@anim/bottom_out</item>
    </style>


</resources>