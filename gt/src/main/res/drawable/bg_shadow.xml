<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 阴影部分 -->
    <!-- 个人觉得更形象的表达：top代表下边的阴影高度，left代表右边的阴影宽度。其实也就是相对应的offset，solid中的颜色是阴影的颜色，也可以设置角度等等 -->
    <item
        android:bottom="2dp"
        android:left="2dp"
        android:right="2dp"
        android:top="2dp">
        <shape android:shape="rectangle">
            <gradient
                android:angle="270"
                android:endColor="#0F000000"
                android:startColor="#0F000000" />
            <corners
                android:bottomLeftRadius="6dip"
                android:bottomRightRadius="6dip"
                android:topLeftRadius="6dip"
                android:topRightRadius="6dip" />
        </shape>
    </item>
    <!-- 背景部分 -->
    <!-- 形象的表达：bottom代表背景部分在上边缘超出阴影的高度，right代表背景部分在左边超出阴影的宽度（相对应的offset） -->
    <item
        android:bottom="5dp"
        android:left="3dp"
        android:right="3dp"
        android:top="3dp">
        <shape android:shape="rectangle">
            <gradient
                android:angle="270"
                android:endColor="#FFFFFF"
                android:startColor="#FFFFFF" />
            <corners
                android:bottomLeftRadius="6dip"
                android:bottomRightRadius="6dip"
                android:topLeftRadius="6dip"
                android:topRightRadius="6dip" />
        </shape>
    </item>
</layer-list>