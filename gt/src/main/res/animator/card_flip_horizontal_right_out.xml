<!--
  Copyright 2012 The Android Open Source Project

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->

<!--
    This object animator is used as a custom fragment transition. See
    FragmentTransaction.setCustomAnimation for more details.

    The overall effect of this animator is to rotate the front of the card
    out of view. The order of operations is described below:

    1. The front rotates 90 degrees, from facing the user to being
       zero-width, fully perpendicular to the viewer, facing left.
    2. The front is then made invisible (this is half-way through the
       animation).
    3. The front rotates another 90 degrees, from zero-width, to
       100% of its normal width, but facing away from the user and
       still invisible.

    This is accomplished using the 2 child animators below, executed in
    parallel. Note that the last animator starts half-way into the animation.
-->

<set xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Rotate. -->
    <objectAnimator
        android:valueFrom="0"
        android:valueTo="180"
        android:propertyName="rotationY"
        android:interpolator="@android:interpolator/accelerate_decelerate"
        android:duration="@integer/card_flip_time_full" />

    <!-- Half-way through the rotation (see startOffset), set the alpha to 0. -->
    <objectAnimator
        android:valueFrom="1.0"
        android:valueTo="0.0"
        android:propertyName="alpha"
        android:startOffset="@integer/card_flip_time_half"
        android:duration="1" />
</set>
