pluginManagement {
    repositories {
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public' }
        gradlePluginPortal()
        google()
        mavenCentral()
        maven { url "https://jitpack.io" }
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public' }
        google()
        mavenCentral()
        maven { url "https://jitpack.io" }
        flatDir {
            dirs 'app/libs'
        }
    }
}
rootProject.name = "android-cashier-helper"
include ':app'
include ':libyxlcommon'
include ':gt'
include ':gt-DataBinding'
include ':libupdate'
