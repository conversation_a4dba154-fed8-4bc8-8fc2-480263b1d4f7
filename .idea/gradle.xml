<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="GradleMigrationSettings" migrationVersion="1" />
  <component name="GradleSettings">
    <option name="linkedExternalProjectsSettings">
      <GradleProjectSettings>
        <option name="testRunner" value="GRADLE" />
        <option name="distributionType" value="DEFAULT_WRAPPED" />
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="gradleJvm" value="Embedded JDK" />
        <option name="modules">
          <set>
            <option value="$PROJECT_DIR$" />
            <option value="$PROJECT_DIR$/app" />
            <option value="$PROJECT_DIR$/gt" />
            <option value="$PROJECT_DIR$/gt-DataBinding" />
            <option value="$PROJECT_DIR$/libupdate" />
            <option value="$PROJECT_DIR$/libyxlcommon" />
          </set>
        </option>
      </GradleProjectSettings>
    </option>
  </component>
</project>